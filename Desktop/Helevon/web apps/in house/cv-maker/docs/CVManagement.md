BaseURL = http://localhost:8000/api/v1

### Key Responses: 
- SuccessResponse (to be used where response model is not given):
```json
{
  "message": "string",
  "data": {}
}
```
- CVListResponse->
```json
{
    "id": "string",
    "user_id": "string",
    "title": "string",
    "template": "string",
    "language": "string",
    "created_at": "2019-08-24T14:15:22Z",
    "updated_at": "2019-08-24T14:15:22Z"
}
```
- CVResponse->
```json
{
  "created_at": "2019-08-24T14:15:22Z",
  "updated_at": "2019-08-24T14:15:22Z",
  "title": "string",
  "template": "string",
  "language": "string",
  "id": "string",
  "user_id": "string",
  "personal_info": {},
  "education": [],
  "work_experience": [],
  "skills": [],
  "references": [],
  "cover_letter": "string"
}
```

-  CVWithFiles
```json
{
  "created_at": "2019-08-24T14:15:22Z",
  "updated_at": "2019-08-24T14:15:22Z",
  "title": "string",
  "template": "string",
  "language": "string",
  "id": "string",
  "user_id": "string",
  "personal_info": {},
  "education": [],
  "work_experience": [],
  "skills": [],
  "references": [],
  "cover_letter": "string",
  "files": []
}
```



# required
Authorizations:
HTTPBearer
HTTP: HTTPBearer
HTTP Authorization Scheme: bearer

# CV Management Session
- Get User Cvs(GET)
route: /cv
Get all CVs for the authenticated user.
Args: current_user: Current authenticated user db: Database session
Returns: List[CVListResponse]: List of user's CVs

- Create Cv(POST)
route: /cv
Create a new CV.
Args: cv_data: CV creation data current_user: Current authenticated user db: Database session
Returns: CVResponse: Created CV information
Raises: HTTPException: If creation fails
payload:
```json
{
  "title": "string",
  "template": "string",
  "language": "string"
}
```
template enum: standard|modern|creative|german
language enum: en|de|ar -> for english, german and arabic

- Get Cv(GET)
route: /cv/{cv_id}
Get a specific CV by ID.
Args: cv_id: CV unique identifier current_user: Current authenticated user db: Database session
Returns: CVWithFiles: CV with associated files
Raises: HTTPException: If CV not found or access denied

- Update Cv(PUT)
route: /cv/{cv_id}
Update a CV.
Args: cv_id: CV unique identifier cv_update: CV update data current_user: Current authenticated user db: Database session
Returns: CVResponse: Updated CV information
Raises: HTTPException: If CV not found or access denied
payload:
```json
{
  "title": "string",
  "template": "string",
  "language": "string"
}
```

- Delete Cv(DELETE)
route: /cv/{cv_id}
Delete a CV.
Args: cv_id: CV unique identifier current_user: Current authenticated user db: Database session
Returns: SuccessResponse: Deletion confirmation
Raises: HTTPException: If CV not found or access denied

- Update Personal Info(PUT)
route: /cv/{cv_id}/personal-info
Update personal information section of a CV.
Args: cv_id: CV unique identifier personal_info: Personal information update data current_user: Current authenticated user db: Database session
Returns: CVResponse: Updated CV information
Raises: HTTPException: If CV not found or access denied
payload:
```json
{
  "firstName": "string",
  "lastName": "string",
  "email": "<EMAIL>",
  "phone": "string",
  "address": "string",
  "city": "string",
  "postalCode": "string",
  "country": "string",
  "dateOfBirth": "string",
  "placeOfBirth": "string",
  "nationality": "string",
  "maritalStatus": "string",
  "summary": "string"
}
```

### For The Below CV update, certificates can be empty, and it can take multiple upload packaged at once except cover letter, for instance, Education below can take in
```json
{
  "education": [
    {
      "id": "string",
      "institution": "string",
      "degree": "string",
      "fieldOfStudy": "string",
      "startDate": "string",
      "endDate": "string",
      "isCurrentlyStudying": false,
      "grade": "string",
      "description": "string",
      "certificates": []
    },
    {
      "id": "string",
      "institution": "string",
      "degree": "string",
      "fieldOfStudy": "string",
      "startDate": "string",
      "endDate": "string",
      "isCurrentlyStudying": false,
      "grade": "string",
      "description": "string",
      "certificates": []
    }, ....
  ]
}
```

- Update Education(PUT)
route: /cv/{cv_id}/education
Update education section of a CV.
Args: cv_id: CV unique identifier education_data: Education update data current_user: Current authenticated user db: Database session
Returns: CVResponse: Updated CV information
payload:
```json
{
  "education": [
    {
      "id": "string",
      "institution": "string",
      "degree": "string",
      "fieldOfStudy": "string",
      "startDate": "string",
      "endDate": "string",
      "isCurrentlyStudying": false,
      "grade": "string",
      "description": "string",
      "certificates": []
    }
  ]
}
```

- Update Work Experience(PUT)
route: /cv/{cv_id}/work-experience
Update work experience section of a CV.
Args: cv_id: CV unique identifier work_data: Work experience update data current_user: Current authenticated user db: Database session
Returns: CVResponse: Updated CV information
payload:
```json
{
  "workExperience": [
    {
      "id": "string",
      "company": "string",
      "position": "string",
      "startDate": "string",
      "endDate": "string",
      "isCurrentlyWorking": false,
      "description": "string",
      "location": "string"
    }
  ]
}
```

- Update Skills(PUT)
route: /cv/{cv_id}/skills
Update skills section of a CV.
Args: cv_id: CV unique identifier skills_data: Skills update data current_user: Current authenticated user db: Database session
Returns: CVResponse: Updated CV information
payload:
```json
{
  "skills": [
    {
      "id": "string",
      "name": "string",
      "category": "string",
      "level": "string"
    }
  ]
}
```
category and level enum
category:technical|language|soft where soft represents soft skills
level:beginner|intermediate|advanced|expert|native

- Update References(PUT)
route: /cv/{cv_id}/references
Update references section of a CV.
Args: cv_id: CV unique identifier references_data: References update data current_user: Current authenticated user db: Database session
Returns: CVResponse: Updated CV information
payload:
```json
{
  "references": [
    {
      "id": "string",
      "name": "string",
      "position": "string",
      "company": "string",
      "email": "<EMAIL>",
      "phone": "string"
    }
  ]
}
```

- Update Cover Letter
route: /cv/{cv_id}/cover-letter
Update cover letter section of a CV.
Args: cv_id: CV unique identifier cover_letter_data: Cover letter update data current_user: Current authenticated user db: Database session
Returns: CVResponse: Updated CV information
payload:
```json
{
  "coverLetter": {
    "recipientName": "string",
    "company": "string",
    "address": "string",
    "postalCode": "string",
    "city": "string",
    "country": "string",
    "email": "string",
    "phone": "string",
    "otherInformation": "string",
    "subject": "string",
    "date": "string",
    "content": "string",
    "signatureFileId": "string"
  }
}
```

- Export Cv Pdf(GET)[For faster dev, should be used to preview and the result can be downloaded by user if okay, else back to editing.]
route: /cv/{cv_id}/export
Generate and download CV as PDF with enhanced template support.
Args: cv_id: CV unique identifier template_id: Template ID override (uses CV template if not provided) include_certificates: Include certificate attachments include_cover_letter: Include cover letter primary_color: Primary color override (hex format) format: Export format (default: pdf) current_user: Current authenticated user db: Database session
Returns: Response: PDF file with Content-Type: application/pdf
Raises: HTTPException: If CV not found, access denied, template invalid, or export disabled
query Parameters
-- template_id:	string (Template Id)	Template ID override[Template management in TemplateManagement.md]
-- include_certificates:	boolean (Include Certificates)	Default: true	Include certificate attachments
-- include_cover_letter:	 boolean (Include Cover Letter)	Default: true	Include cover letter
-- primary_color	string (Primary Color) ^#[0-9A-Fa-f]{6}$	Primary color override format	string (Format) [In UI use color picker for ease, default HexColor(0x00008B)]
Default: "pdf"
Export format
