export interface CVListItem {
  id: string;
  user_id: string;
  title: string;
  template: 'standard' | 'modern' | 'creative' | 'german';
  language: 'en' | 'de' | 'ar';
  created_at: string;
  updated_at: string;
}

export interface PersonalInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  postalCode: string;
  country: string;
  dateOfBirth: string;
  placeOfBirth: string;
  nationality: string;
  maritalStatus: string;
  summary: string;
}

export interface Education {
  id: string;
  institution: string;
  degree: string;
  fieldOfStudy: string;
  startDate: string;
  endDate: string;
  isCurrentlyStudying: boolean;
  grade: string;
  description: string;
  certificates: string[];
}

export interface WorkExperience {
  id: string;
  company: string;
  position: string;
  startDate: string;
  endDate: string;
  isCurrentlyWorking: boolean;
  description: string;
  location: string;
}

export interface Skill {
  id: string;
  name: string;
  category: 'technical' | 'language' | 'soft';
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert' | 'native';
}

export interface Reference {
  id: string;
  name: string;
  position: string;
  company: string;
  email: string;
  phone: string;
}

export interface CoverLetter {
  recipientName: string;
  company: string;
  address: string;
  postalCode: string;
  city: string;
  country: string;
  email: string;
  phone: string;
  otherInformation: string;
  subject: string;
  date: string;
  content: string;
}

export interface CVFile {
  id: string;
  filename: string;
  original_filename: string;
  file_type: string;
  file_size: number;
  category: string;
  uploaded_at: string;
}

export interface CV {
  created_at: string;
  updated_at: string;
  title: string;
  template: 'standard' | 'modern' | 'creative' | 'german';
  language: 'en' | 'de' | 'ar';
  id: string;
  user_id: string;
  personal_info: PersonalInfo | null;
  education: Education[];
  work_experience: WorkExperience[];
  skills: Skill[];
  references: Reference[];
  cover_letter: CoverLetter | null;
}

export interface CVWithFiles extends CV {
  files: CVFile[];
}

export interface CreateCVPayload {
  title: string;
  template: 'standard' | 'modern' | 'creative' | 'german';
  language: 'en' | 'de' | 'ar';
}

export interface UpdateCVPayload {
  title?: string;
  template?: 'standard' | 'modern' | 'creative' | 'german';
  language?: 'en' | 'de' | 'ar';
}

export interface CVState {
  cvs: CVListItem[];
  currentCV: CVWithFiles | null;
  isLoading: boolean;
  error: string | null;
}

export interface CVActions {
  fetchCVs: () => Promise<void>;
  fetchCV: (id: string) => Promise<void>;
  createCV: (data: CreateCVPayload) => Promise<string>;
  updateCV: (id: string, data: UpdateCVPayload) => Promise<void>;
  deleteCV: (id: string) => Promise<void>;
  updatePersonalInfo: (cvId: string, data: PersonalInfo) => Promise<void>;
  updateEducation: (cvId: string, data: { education: Education[] }) => Promise<void>;
  updateWorkExperience: (cvId: string, data: { work_experience: WorkExperience[] }) => Promise<void>;
  updateSkills: (cvId: string, data: { skills: Skill[] }) => Promise<void>;
  updateReferences: (cvId: string, data: { references: Reference[] }) => Promise<void>;
  updateCoverLetter: (cvId: string, data: CoverLetter) => Promise<void>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

export type CVStore = CVState & CVActions;
