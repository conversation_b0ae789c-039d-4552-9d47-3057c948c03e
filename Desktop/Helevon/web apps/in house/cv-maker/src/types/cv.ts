export interface CVListItem {
  id: string;
  user_id: string;
  title: string;
  template: 'standard' | 'modern' | 'creative' | 'german';
  language: 'en' | 'de' | 'ar';
  created_at: string;
  updated_at: string;
}

export interface PersonalInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  postalCode: string;
  country: string;
  dateOfBirth: string;
  placeOfBirth: string;
  nationality: string;
  maritalStatus: string;
  summary: string;
}

export interface Education {
  id: string;
  institution: string;
  degree: string;
  fieldOfStudy: string;
  startDate: string;
  endDate: string;
  isCurrentlyStudying: boolean;
  grade: string;
  description: string;
  certificates: string[];
}

export interface WorkExperience {
  id: string;
  company: string;
  position: string;
  startDate: string;
  endDate: string;
  isCurrentlyWorking: boolean;
  description: string;
  location: string;
}

export interface Skill {
  id: string;
  name: string;
  category: 'technical' | 'language' | 'soft';
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert' | 'native';
}

export interface Reference {
  id: string;
  name: string;
  position: string;
  company: string;
  email: string;
  phone: string;
}

export interface CoverLetter {
  recipientName: string;
  company: string;
  address: string;
  postalCode: string;
  city: string;
  country: string;
  email: string;
  phone: string;
  otherInformation: string;
  subject: string;
  date: string;
  content: string;
  signatureFileId?: string;
}

// File Management Types - Exact match with FileManagement.md
export interface FileResponse {
  created_at: string;
  updated_at: string;
  name: string;
  type: string;
  size: number;
  category: string;
  id: string;
  user_id: string;
  cv_id: string;
  url: string;
}

export interface FileDeleteResponse {
  message: string;
  file_id: string;
}

export interface FileListResponse {
  id: string;
  name: string;
  type: string;
  size: number;
  category: string;
  url: string;
  created_at: string;
}

export interface SuccessResponse {
  message: string;
  data: any;
}

// Legacy interface for backward compatibility
export interface CVFile {
  id: string;
  filename: string;
  original_filename: string;
  file_type: string;
  file_size: number;
  category: string;
  uploaded_at: string;
}

export interface CV {
  created_at: string;
  updated_at: string;
  title: string;
  template: 'standard' | 'modern' | 'creative' | 'german';
  language: 'en' | 'de' | 'ar';
  id: string;
  user_id: string;
  personal_info: PersonalInfo | any; // Can be PersonalInfo object or empty object {}
  education: Education[] | any[]; // Can be Education[] or empty array []
  work_experience: WorkExperience[] | any[]; // Can be WorkExperience[] or empty array []
  skills: Skill[] | any[]; // Can be Skill[] or empty array []
  references: Reference[] | any[]; // Can be Reference[] or empty array []
  cover_letter: CoverLetter | string | null; // Can be CoverLetter object, string, or null
}

export interface CVWithFiles extends CV {
  files: CVFile[];
}

export interface CreateCVPayload {
  title: string;
  template: 'standard' | 'modern' | 'creative' | 'german';
  language: 'en' | 'de' | 'ar';
}

export interface UpdateCVPayload {
  title?: string;
  template?: 'standard' | 'modern' | 'creative' | 'german';
  language?: 'en' | 'de' | 'ar';
}

export interface CVState {
  cvs: CVListItem[];
  currentCV: CVWithFiles | null;
  isLoading: boolean;
  error: string | null;
}

// Export Types - Based on CVManagement.md
export interface ExportOptions {
  template_id?: string;
  include_certificates?: boolean;
  include_cover_letter?: boolean;
  primary_color?: string;
  format?: string;
}

export interface CVActions {
  fetchCVs: () => Promise<void>;
  fetchCV: (id: string) => Promise<void>;
  createCV: (data: CreateCVPayload) => Promise<string>;
  updateCV: (id: string, data: UpdateCVPayload) => Promise<void>;
  deleteCV: (id: string) => Promise<void>;
  updatePersonalInfo: (cvId: string, data: PersonalInfo) => Promise<void>;
  updateEducation: (cvId: string, data: { education: Education[] }) => Promise<void>;
  updateWorkExperience: (cvId: string, data: { work_experience: WorkExperience[] }) => Promise<void>;
  updateSkills: (cvId: string, data: { skills: Skill[] }) => Promise<void>;
  updateReferences: (cvId: string, data: { references: Reference[] }) => Promise<void>;
  updateCoverLetter: (cvId: string, data: CoverLetter) => Promise<void>;
  exportCV: (cvId: string, options?: ExportOptions) => Promise<Blob>;
  uploadFile: (cvId: string, file: File, category: string) => Promise<FileResponse>;
  deleteFile: (fileId: string) => Promise<void>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

export type CVStore = CVState & CVActions;
