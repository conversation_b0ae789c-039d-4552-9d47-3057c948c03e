export interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  preview_image: string;
  is_premium: boolean;
  created_at: string;
  updated_at: string;
}

export interface TemplateState {
  templates: Template[];
  isLoading: boolean;
  error: string | null;
}

export interface TemplateActions {
  fetchTemplates: () => Promise<void>;
  getTemplatePreview: (templateId: string) => Promise<string>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

export type TemplateStore = TemplateState & TemplateActions;
