export interface CVTemplate {
  id: string;
  name: string;
  description: string;
  category: 'professional' | 'academic' | 'creative' | 'technical';
  languages: ('en' | 'de' | 'ar')[];
  features: string[];
  preview_url: string;
  is_premium: boolean;
  cultural_notes: {
    [key: string]: string;
  };
}

export interface TemplatePreview {
  id: string;
  template_id: string;
  language: 'en' | 'de' | 'ar';
  preview_url: string;
  sample_data: {
    name: string;
    title: string;
    email: string;
    phone: string;
    location: string;
  };
}

export interface TemplateCustomization {
  primary_color: string;
  font_family: string;
  font_size: 'small' | 'medium' | 'large';
  spacing: 'compact' | 'standard' | 'spacious';
  include_photo: boolean;
  section_order: string[];
}

export interface TemplateCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  template_count: number;
}
