export interface LoginPayload {
  email: string;
  password: string;
}

export interface RegisterPayload {
  name: string;
  email: string;
  language: 'en' | 'de' | 'ar';
  password: string;
  confirm_password: string;
}

export interface TokenResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
}

export interface RefreshTokenPayload {
  refresh_token: string;
}

export interface UserResponse {
  created_at: string;
  updated_at: string;
  name: string;
  email: string;
  language: string;
  id: string;
  email_verified: string | null;
  last_login_at: string | null;
}

export interface UserUpdatePayload {
  name?: string;
  email?: string;
  language?: 'en' | 'de' | 'ar';
  current_password?: string;
  new_password?: string;
  confirm_password?: string;
}

export interface UserProfile extends UserResponse {
  login_attempts: number;
  locked_until: string | null;
}

export interface AuthState {
  user: UserResponse | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface AuthActions {
  login: (credentials: LoginPayload) => Promise<void>;
  register: (userData: RegisterPayload) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  updateAccount: (userData: UserUpdatePayload) => Promise<void>;
  fetchUserAccount: () => Promise<void>;
  fetchUserProfile: () => Promise<UserProfile>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

export type AuthStore = AuthState & AuthActions;
