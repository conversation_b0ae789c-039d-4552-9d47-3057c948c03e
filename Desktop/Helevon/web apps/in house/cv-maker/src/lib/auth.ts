import { TOKEN_STORAGE_KEYS, ENCRYPTION_KEY } from './constants';
import { encryptTokenSync, decryptTokenSync, isTokenExpired } from './utils';

// Token storage utilities with strong encryption
export function setTokens(accessToken: string, refreshToken: string): void {
  try {
    // Use synchronous encryption for immediate storage
    const encryptedAccessToken = encryptTokenSync(accessToken, ENCRYPTION_KEY);
    const encryptedRefreshToken = encryptTokenSync(refreshToken, ENCRYPTION_KEY);

    // Store access token in sessionStorage (cleared when browser closes)
    sessionStorage.setItem(TOKEN_STORAGE_KEYS.ACCESS_TOKEN, encryptedAccessToken);

    // Store refresh token in localStorage (persists across sessions)
    localStorage.setItem(TOKEN_STORAGE_KEYS.REFRESH_TOKEN, encryptedRefreshToken);

    // Log for debugging (remove in production)
    console.log('Tokens stored securely (encrypted)');
  } catch (error) {
    console.error('Error storing tokens:', error);
  }
}

export function getAccessToken(): string | null {
  try {
    const encryptedToken = sessionStorage.getItem(TOKEN_STORAGE_KEYS.ACCESS_TOKEN);
    if (!encryptedToken) return null;

    const token = decryptTokenSync(encryptedToken, ENCRYPTION_KEY);
    return token || null;
  } catch (error) {
    console.error('Error retrieving access token:', error);
    return null;
  }
}

export function getRefreshToken(): string | null {
  try {
    const encryptedToken = localStorage.getItem(TOKEN_STORAGE_KEYS.REFRESH_TOKEN);
    if (!encryptedToken) return null;

    const token = decryptTokenSync(encryptedToken, ENCRYPTION_KEY);
    return token || null;
  } catch (error) {
    console.error('Error retrieving refresh token:', error);
    return null;
  }
}

export function clearTokens(): void {
  try {
    sessionStorage.removeItem(TOKEN_STORAGE_KEYS.ACCESS_TOKEN);
    localStorage.removeItem(TOKEN_STORAGE_KEYS.REFRESH_TOKEN);
  } catch (error) {
    console.error('Error clearing tokens:', error);
  }
}

export function isAuthenticated(): boolean {
  const accessToken = getAccessToken();
  const refreshToken = getRefreshToken();
  
  if (!accessToken && !refreshToken) {
    return false;
  }
  
  // If access token exists and is not expired, user is authenticated
  if (accessToken && !isTokenExpired(accessToken)) {
    return true;
  }
  
  // If refresh token exists and is not expired, user can be re-authenticated
  if (refreshToken && !isTokenExpired(refreshToken)) {
    return true;
  }
  
  return false;
}

export function shouldRefreshToken(): boolean {
  const accessToken = getAccessToken();
  const refreshToken = getRefreshToken();
  
  // If no refresh token, can't refresh
  if (!refreshToken || isTokenExpired(refreshToken)) {
    return false;
  }
  
  // If no access token or access token is expired, should refresh
  if (!accessToken || isTokenExpired(accessToken)) {
    return true;
  }
  
  return false;
}

// Get user info from token payload
export function getUserFromToken(): any | null {
  try {
    const accessToken = getAccessToken();
    if (!accessToken) return null;
    
    const payload = JSON.parse(atob(accessToken.split('.')[1]));
    return payload.user || payload.sub || null;
  } catch (error) {
    console.error('Error parsing user from token:', error);
    return null;
  }
}
