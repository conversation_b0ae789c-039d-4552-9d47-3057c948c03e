export const BASE_URL = 'http://localhost:8000/api/v1';

export const SUPPORTED_LANGUAGES = {
  en: 'English',
  de: 'Deutsch',
  ar: 'العربية'
} as const;

export const CV_TEMPLATES = {
  standard: 'Standard',
  modern: 'Modern',
  creative: 'Creative',
  german: 'German'
} as const;

export const SKILL_CATEGORIES = {
  technical: 'Technical',
  language: 'Language',
  soft: 'Soft'
} as const;

export const SKILL_LEVELS = {
  beginner: 'Beginner',
  intermediate: 'Intermediate',
  advanced: 'Advanced',
  expert: 'Expert',
  native: 'Native'
} as const;

export const TOKEN_STORAGE_KEYS = {
  ACCESS_TOKEN: 'cv_maker_access_token',
  REFRESH_TOKEN: 'cv_maker_refresh_token'
} as const;

export const ROUTES = {
  HOME: '/',
  LOGIN: '/auth/login',
  REGISTER: '/auth/register',
  FORGOT_PASSWORD: '/auth/forgot-password',
  DASHBOARD: '/dashboard',
  PROFILE: '/profile',
  CV_CREATE: '/cv/create',
  CV_EDIT: '/cv/[id]/edit',
  CV_PREVIEW: '/cv/[id]/preview',
  TERMS: '/legal/terms',
  PRIVACY: '/legal/privacy'
} as const;

// Simple encryption key for demonstration purposes
// In production, this should be environment-specific and more secure
export const ENCRYPTION_KEY = 'cv-maker-demo-key-2024';
