import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Secure encryption/decryption utilities using Web Crypto API
// This provides proper AES-GCM encryption for sensitive data
export async function encryptToken(token: string, key: string): Promise<string> {
  try {
    // Create a key from the provided string
    const encoder = new TextEncoder();
    const keyData = encoder.encode(key.padEnd(32, '0').substring(0, 32)); // Ensure 32 bytes

    const cryptoKey = await crypto.subtle.importKey(
      'raw',
      keyData,
      { name: 'AES-GCM' },
      false,
      ['encrypt']
    );

    // Generate a random IV
    const iv = crypto.getRandomValues(new Uint8Array(12));

    // Encrypt the token
    const encodedToken = encoder.encode(token);
    const encrypted = await crypto.subtle.encrypt(
      { name: 'AES-GCM', iv },
      cryptoKey,
      encodedToken
    );

    // Combine IV and encrypted data
    const combined = new Uint8Array(iv.length + encrypted.byteLength);
    combined.set(iv);
    combined.set(new Uint8Array(encrypted), iv.length);

    // Convert to base64 for storage
    return btoa(String.fromCharCode(...combined));
  } catch (error) {
    console.error('Encryption failed:', error);
    // Fallback to simple encoding (not secure, but prevents app crash)
    return btoa(token);
  }
}

export async function decryptToken(encryptedToken: string, key: string): Promise<string> {
  try {
    // Decode from base64
    const combined = new Uint8Array(
      atob(encryptedToken).split('').map(char => char.charCodeAt(0))
    );

    // Extract IV and encrypted data
    const iv = combined.slice(0, 12);
    const encrypted = combined.slice(12);

    // Create key
    const encoder = new TextEncoder();
    const keyData = encoder.encode(key.padEnd(32, '0').substring(0, 32));

    const cryptoKey = await crypto.subtle.importKey(
      'raw',
      keyData,
      { name: 'AES-GCM' },
      false,
      ['decrypt']
    );

    // Decrypt
    const decrypted = await crypto.subtle.decrypt(
      { name: 'AES-GCM', iv },
      cryptoKey,
      encrypted
    );

    // Convert back to string
    const decoder = new TextDecoder();
    return decoder.decode(decrypted);
  } catch (error) {
    console.error('Decryption failed:', error);
    // Try fallback decoding
    try {
      return atob(encryptedToken);
    } catch {
      return '';
    }
  }
}

// Synchronous versions for backward compatibility (less secure)
export function encryptTokenSync(token: string, key: string): string {
  // XOR encryption with multiple rounds for better security
  let encrypted = token;
  const rounds = 3;

  for (let round = 0; round < rounds; round++) {
    let result = '';
    const keyToUse = key + round.toString();

    for (let i = 0; i < encrypted.length; i++) {
      const charCode = encrypted.charCodeAt(i);
      const keyChar = keyToUse.charCodeAt(i % keyToUse.length);
      result += String.fromCharCode(charCode ^ keyChar ^ (i % 256));
    }
    encrypted = result;
  }

  // Add random padding and encode
  const padding = Math.random().toString(36).substring(2, 8);
  return btoa(padding + '|' + encrypted + '|' + padding.split('').reverse().join(''));
}

export function decryptTokenSync(encryptedToken: string, key: string): string {
  try {
    // Decode and remove padding
    const decoded = atob(encryptedToken);
    const parts = decoded.split('|');
    if (parts.length !== 3) return '';

    let encrypted = parts[1];
    const rounds = 3;

    // Reverse the encryption rounds
    for (let round = rounds - 1; round >= 0; round--) {
      let result = '';
      const keyToUse = key + round.toString();

      for (let i = 0; i < encrypted.length; i++) {
        const charCode = encrypted.charCodeAt(i);
        const keyChar = keyToUse.charCodeAt(i % keyToUse.length);
        result += String.fromCharCode(charCode ^ keyChar ^ (i % 256));
      }
      encrypted = result;
    }

    return encrypted;
  } catch (error) {
    console.error('Sync decryption failed:', error);
    return '';
  }
}

export function formatDate(dateString: string, locale: string = 'en-US'): string {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString(locale);
  } catch {
    return dateString;
  }
}

export function formatDateTime(dateString: string, locale: string = 'en-US'): string {
  try {
    const date = new Date(dateString);
    return date.toLocaleString(locale);
  } catch {
    return dateString;
  }
}

export function isTokenExpired(token: string): boolean {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Math.floor(Date.now() / 1000);
    return payload.exp < currentTime;
  } catch {
    return true;
  }
}

export function getTokenExpirationTime(token: string): number | null {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload.exp * 1000; // Convert to milliseconds
  } catch {
    return null;
  }
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function validatePassword(password: string): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

export function generateId(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
}
