'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { ArrowRight, CheckCircle, Star, Zap, Globe, Briefcase, FileText, Download, Sparkles, Rocket, Target, TrendingUp } from 'lucide-react';
import { MainLayout } from '@/components/layout/MainLayout';
import { Button } from '@/components/ui/Button';
import {
  FadeInUp,
  FadeInScale,
  SlideInLeft,
  SlideInRight,
  StaggerContainer,
  FloatingElement,
  PulsingElement,
  MagneticElement,
  ParallaxElement
} from '@/components/motion/MotionComponents';
import { ROUTES } from '@/lib/constants';

export default function Home() {
  return (
    <MainLayout>
      {/* Hero Section - Completely Reimagined */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Animated Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-background to-accent/10">
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(14,165,233,0.1),transparent_50%)]" />
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(139,92,246,0.1),transparent_50%)]" />
        </div>

        {/* Floating Elements */}
        <FloatingElement className="absolute top-20 left-10 opacity-20" intensity={15} duration={6}>
          <div className="w-20 h-20 rounded-full bg-gradient-to-r from-primary to-accent blur-xl" />
        </FloatingElement>
        <FloatingElement className="absolute bottom-20 right-10 opacity-20" intensity={20} duration={8}>
          <div className="w-32 h-32 rounded-full bg-gradient-to-r from-accent to-primary blur-2xl" />
        </FloatingElement>
        <FloatingElement className="absolute top-1/2 right-20 opacity-10" intensity={10} duration={5}>
          <div className="w-16 h-16 rounded-full bg-gradient-to-r from-success to-warning blur-xl" />
        </FloatingElement>

        <div className="container mx-auto px-4 py-20 relative z-10">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Left Content */}
            <StaggerContainer className="space-y-8">
              <FadeInUp>
                <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-primary/10 to-accent/10 border border-primary/20 mb-6">
                  <Sparkles className="h-4 w-4 text-primary" />
                  <span className="text-sm font-medium text-primary">AI-Powered CV Builder</span>
                </div>
              </FadeInUp>

              <FadeInUp delay={0.1}>
                <h1 className="text-5xl lg:text-7xl font-bold tracking-tight leading-tight">
                  Craft Your{' '}
                  <span className="relative">
                    <span className="gradient-text bg-gradient-to-r from-primary via-accent to-primary bg-[length:200%_200%] animate-gradient">
                      Dream Career
                    </span>
                    <motion.div
                      className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-primary to-accent rounded-full"
                      initial={{ scaleX: 0 }}
                      animate={{ scaleX: 1 }}
                      transition={{ delay: 1, duration: 0.8 }}
                    />
                  </span>
                </h1>
              </FadeInUp>

              <FadeInUp delay={0.2}>
                <p className="text-xl lg:text-2xl text-foreground-muted max-w-2xl leading-relaxed">
                  Transform your professional story with our revolutionary CV builder.
                  Create stunning, ATS-optimized resumes that land interviews.
                </p>
              </FadeInUp>

              <FadeInUp delay={0.3}>
                <div className="flex flex-col sm:flex-row gap-4">
                  <MagneticElement>
                    <Button
                      size="xl"
                      asChild
                      href={ROUTES.REGISTER}
                      gradient
                      glow
                      className="group"
                    >
                      <Rocket className="mr-2 h-5 w-5 group-hover:animate-bounce" />
                      Start Building Now
                      <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </MagneticElement>

                  <MagneticElement>
                    <Button
                      variant="glass"
                      size="xl"
                      asChild
                      href="#preview"
                      className="group"
                    >
                      <FileText className="mr-2 h-5 w-5" />
                      See Examples
                    </Button>
                  </MagneticElement>
                </div>
              </FadeInUp>

              <FadeInUp delay={0.4}>
                <div className="flex items-center gap-8 pt-4">
                  {[
                    { icon: CheckCircle, text: "100% Free Forever", color: "text-success" },
                    { icon: Zap, text: "AI-Powered", color: "text-warning" },
                    { icon: Globe, text: "Multi-Language", color: "text-primary" }
                  ].map((item, index) => (
                    <motion.div
                      key={index}
                      className="flex items-center gap-2"
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: 0.6 + index * 0.1, duration: 0.5 }}
                    >
                      <item.icon className={`h-5 w-5 ${item.color}`} />
                      <span className="text-sm font-medium">{item.text}</span>
                    </motion.div>
                  ))}
                </div>
              </FadeInUp>
            </StaggerContainer>

            {/* Right Content - Interactive CV Preview */}
            <SlideInRight className="relative">
              <div className="relative">
                {/* Main CV Card */}
                <MagneticElement strength={15}>
                  <motion.div
                    className="relative bg-background-elevated rounded-3xl shadow-2xl p-8 border border-border hover-lift"
                    whileHover={{
                      rotateY: 5,
                      rotateX: 5,
                      scale: 1.02,
                    }}
                    transition={{ type: "spring", stiffness: 300, damping: 30 }}
                    style={{ transformStyle: "preserve-3d" }}
                  >
                    {/* CV Content with Animations */}
                    <div className="space-y-6">
                      <motion.div
                        className="h-6 bg-gradient-to-r from-primary to-accent rounded-lg"
                        initial={{ width: 0 }}
                        animate={{ width: "75%" }}
                        transition={{ delay: 1, duration: 1 }}
                      />

                      <motion.div
                        className="h-4 bg-secondary rounded w-1/2"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 1.2, duration: 0.5 }}
                      />

                      <div className="space-y-3">
                        {[100, 85, 70].map((width, index) => (
                          <motion.div
                            key={index}
                            className="h-3 bg-border rounded"
                            initial={{ width: 0 }}
                            animate={{ width: `${width}%` }}
                            transition={{ delay: 1.4 + index * 0.2, duration: 0.8 }}
                          />
                        ))}
                      </div>

                      <div className="pt-4 space-y-3">
                        <motion.div
                          className="h-4 bg-gradient-to-r from-accent to-warning rounded w-2/3"
                          initial={{ width: 0 }}
                          animate={{ width: "66%" }}
                          transition={{ delay: 2.2, duration: 0.8 }}
                        />
                        {[90, 75].map((width, index) => (
                          <motion.div
                            key={index}
                            className="h-3 bg-border rounded"
                            initial={{ width: 0 }}
                            animate={{ width: `${width}%` }}
                            transition={{ delay: 2.4 + index * 0.2, duration: 0.6 }}
                          />
                        ))}
                      </div>
                    </div>

                    {/* Floating Action Buttons */}
                    <motion.div
                      className="absolute -top-4 -right-4 bg-success text-white rounded-full p-3 shadow-lg"
                      animate={{ rotate: [0, 10, -10, 0] }}
                      transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
                    >
                      <Download className="h-5 w-5" />
                    </motion.div>
                  </motion.div>
                </MagneticElement>

                {/* Floating Stats */}
                <FloatingElement className="absolute -top-8 -left-8" intensity={8} duration={4}>
                  <div className="glass rounded-2xl p-4 border border-white/20">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-5 w-5 text-success" />
                      <span className="text-sm font-semibold">98% Success Rate</span>
                    </div>
                  </div>
                </FloatingElement>

                <FloatingElement className="absolute -bottom-8 -right-8" intensity={12} duration={5}>
                  <div className="glass rounded-2xl p-4 border border-white/20">
                    <div className="flex items-center gap-2">
                      <Target className="h-5 w-5 text-primary" />
                      <span className="text-sm font-semibold">ATS Optimized</span>
                    </div>
                  </div>
                </FloatingElement>
              </div>
            </SlideInRight>
          </div>
        </div>

        {/* Scroll Indicator */}
        <motion.div
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <div className="w-6 h-10 border-2 border-primary rounded-full flex justify-center">
            <motion.div
              className="w-1 h-3 bg-primary rounded-full mt-2"
              animate={{ y: [0, 12, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
            />
          </div>
        </motion.div>
      </section>

      {/* Revolutionary Features Section */}
      <section id="features" className="py-32 relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-b from-background via-secondary/20 to-background" />
        <ParallaxElement speed={0.3} className="absolute inset-0 opacity-30">
          <div className="absolute top-20 left-20 w-72 h-72 bg-gradient-to-r from-primary/20 to-accent/20 rounded-full blur-3xl" />
          <div className="absolute bottom-20 right-20 w-96 h-96 bg-gradient-to-r from-accent/20 to-success/20 rounded-full blur-3xl" />
        </ParallaxElement>

        <div className="container mx-auto px-4 relative z-10">
          <StaggerContainer>
            <FadeInUp className="text-center space-y-6 mb-20">
              <motion.div
                className="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-gradient-to-r from-primary/10 to-accent/10 border border-primary/20"
                whileHover={{ scale: 1.05 }}
              >
                <Sparkles className="h-5 w-5 text-primary" />
                <span className="font-medium text-primary">Revolutionary Features</span>
              </motion.div>

              <h2 className="text-4xl lg:text-6xl font-bold tracking-tight">
                Everything You Need to{' '}
                <span className="gradient-text bg-gradient-to-r from-primary via-accent to-primary bg-[length:200%_200%] animate-gradient">
                  Dominate Your Career
                </span>
              </h2>

              <p className="text-xl lg:text-2xl text-foreground-muted max-w-3xl mx-auto leading-relaxed">
                Our AI-powered platform combines cutting-edge technology with beautiful design
                to create CVs that get you hired faster.
              </p>
            </FadeInUp>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mt-20">
              {[
                {
                  icon: FileText,
                  title: "Premium Templates",
                  description: "Industry-specific templates designed by HR experts and optimized for ATS systems.",
                  gradient: "from-primary to-primary-dark",
                  delay: 0.1,
                  comingSoon: false
                },
                {
                  icon: Globe,
                  title: "Global Reach",
                  description: "Multi-language support with cultural formatting for international opportunities.",
                  gradient: "from-accent to-accent-dark",
                  delay: 0.2,
                  comingSoon: false
                },
                {
                  icon: Download,
                  title: "Instant Export",
                  description: "High-quality PDF generation with perfect formatting across all devices.",
                  gradient: "from-success to-success-light",
                  delay: 0.3,
                  comingSoon: false
                },
                {
                  icon: Zap,
                  title: "AI-Powered Editor",
                  description: "Smart suggestions and real-time optimization to maximize your interview chances.",
                  gradient: "from-warning to-warning-light",
                  delay: 0.4,
                  comingSoon: false
                },
                {
                  icon: Briefcase,
                  title: "Direct Job Applications",
                  description: "Apply to jobs directly from your CV with personalized cover letters.",
                  gradient: "from-accent to-destructive",
                  delay: 0.5,
                  comingSoon: true,
                  highlight: true
                },
                {
                  icon: Star,
                  title: "Smart Job Matching",
                  description: "AI-powered job discovery and automated applications to your dream companies.",
                  gradient: "from-primary to-accent",
                  delay: 0.6,
                  comingSoon: true,
                  highlight: true
                }
              ].map((feature, index) => (
                <FadeInUp key={index} delay={feature.delay}>
                  <MagneticElement>
                    <motion.div
                      className={`relative group h-full ${
                        feature.highlight
                          ? 'bg-gradient-to-br from-accent/5 to-primary/5 border-2 border-accent/20'
                          : 'bg-background-elevated border border-border'
                      } rounded-3xl p-8 hover-lift overflow-hidden`}
                      whileHover={{
                        scale: 1.02,
                        rotateY: feature.highlight ? 5 : 0,
                      }}
                      transition={{ type: "spring", stiffness: 300, damping: 30 }}
                    >
                      {/* Background Glow */}
                      <div className={`absolute inset-0 bg-gradient-to-r ${feature.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-500 rounded-3xl`} />

                      {/* Icon */}
                      <div className={`relative mb-6 inline-flex p-4 rounded-2xl bg-gradient-to-r ${feature.gradient} shadow-lg`}>
                        <feature.icon className="h-8 w-8 text-white" />
                        {feature.highlight && (
                          <motion.div
                            className="absolute -top-1 -right-1 w-3 h-3 bg-accent rounded-full"
                            animate={{ scale: [1, 1.2, 1] }}
                            transition={{ duration: 2, repeat: Infinity }}
                          />
                        )}
                      </div>

                      {/* Content */}
                      <div className="relative z-10">
                        <div className="flex items-center gap-3 mb-4">
                          <h3 className="text-2xl font-bold">{feature.title}</h3>
                          {feature.comingSoon && (
                            <motion.span
                              className="px-3 py-1 text-xs font-bold bg-gradient-to-r from-accent to-accent-dark text-white rounded-full"
                              animate={{
                                boxShadow: [
                                  '0 0 0 0 rgba(139, 92, 246, 0.4)',
                                  '0 0 0 10px rgba(139, 92, 246, 0)',
                                ]
                              }}
                              transition={{ duration: 2, repeat: Infinity }}
                            >
                              Coming Soon
                            </motion.span>
                          )}
                        </div>

                        <p className="text-foreground-muted leading-relaxed">
                          {feature.description}
                        </p>
                      </div>

                      {/* Hover Effect */}
                      <div className="absolute inset-0 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />
                      </div>
                    </motion.div>
                  </MagneticElement>
                </FadeInUp>
              ))}
            </div>
          </StaggerContainer>
        </div>
      </section>

      {/* Revolutionary Process Section */}
      <section id="how-it-works" className="py-32 relative overflow-hidden">
        {/* Animated Background */}
        <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-accent/5 to-success/5" />
        <motion.div
          className="absolute inset-0 opacity-30"
          animate={{
            background: [
              'radial-gradient(circle at 20% 50%, rgba(14,165,233,0.1) 0%, transparent 50%)',
              'radial-gradient(circle at 80% 50%, rgba(139,92,246,0.1) 0%, transparent 50%)',
              'radial-gradient(circle at 50% 80%, rgba(16,185,129,0.1) 0%, transparent 50%)',
              'radial-gradient(circle at 20% 50%, rgba(14,165,233,0.1) 0%, transparent 50%)',
            ]
          }}
          transition={{ duration: 10, repeat: Infinity }}
        />

        <div className="container mx-auto px-4 relative z-10">
          <StaggerContainer>
            <FadeInUp className="text-center space-y-6 mb-20">
              <motion.div
                className="inline-flex items-center gap-2 px-6 py-3 rounded-full glass border border-white/20"
                whileHover={{ scale: 1.05 }}
              >
                <Rocket className="h-5 w-5 text-primary" />
                <span className="font-medium">Simple 3-Step Process</span>
              </motion.div>

              <h2 className="text-4xl lg:text-6xl font-bold tracking-tight">
                From Zero to{' '}
                <span className="gradient-text bg-gradient-to-r from-success via-primary to-accent bg-[length:200%_200%] animate-gradient">
                  Hired Hero
                </span>
                <br />in Minutes
              </h2>

              <p className="text-xl lg:text-2xl text-foreground-muted max-w-3xl mx-auto leading-relaxed">
                Our revolutionary AI-powered process transforms your career story
                into a compelling narrative that recruiters can't ignore.
              </p>
            </FadeInUp>

            <div className="grid md:grid-cols-3 gap-12 mt-20">
              {[
                {
                  step: "01",
                  title: "Choose Your Destiny",
                  description: "Select from our AI-curated templates designed by industry experts for maximum impact.",
                  icon: Target,
                  gradient: "from-primary to-primary-dark",
                  delay: 0.1
                },
                {
                  step: "02",
                  title: "AI-Powered Creation",
                  description: "Our intelligent system guides you through each section with smart suggestions and real-time optimization.",
                  icon: Zap,
                  gradient: "from-accent to-accent-dark",
                  delay: 0.2
                },
                {
                  step: "03",
                  title: "Launch Your Career",
                  description: "Export your masterpiece and watch as interview invitations flood your inbox.",
                  icon: Rocket,
                  gradient: "from-success to-success-light",
                  delay: 0.3
                }
              ].map((step, index) => (
                <FadeInUp key={index} delay={step.delay}>
                  <div className="relative group">
                    {/* Connection Line */}
                    {index < 2 && (
                      <motion.div
                        className="hidden md:block absolute top-20 left-full w-12 h-0.5 bg-gradient-to-r from-primary/50 to-transparent z-0"
                        initial={{ scaleX: 0 }}
                        animate={{ scaleX: 1 }}
                        transition={{ delay: 1 + index * 0.3, duration: 0.8 }}
                      />
                    )}

                    <MagneticElement>
                      <motion.div
                        className="relative text-center space-y-6 p-8 rounded-3xl bg-background-elevated border border-border hover-lift group-hover:border-primary/20 transition-all duration-300"
                        whileHover={{
                          y: -10,
                          boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)"
                        }}
                      >
                        {/* Step Number */}
                        <div className="relative">
                          <motion.div
                            className={`w-20 h-20 mx-auto rounded-full bg-gradient-to-r ${step.gradient} flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300`}
                            whileHover={{ scale: 1.1, rotate: 5 }}
                          >
                            <span className="text-2xl font-bold text-white">{step.step}</span>
                          </motion.div>

                          {/* Floating Icon */}
                          <motion.div
                            className="absolute -top-2 -right-2 w-10 h-10 bg-background-elevated rounded-full border-2 border-primary flex items-center justify-center shadow-lg"
                            animate={{
                              rotate: [0, 10, -10, 0],
                              scale: [1, 1.1, 1]
                            }}
                            transition={{
                              duration: 4,
                              repeat: Infinity,
                              delay: index * 0.5
                            }}
                          >
                            <step.icon className="h-5 w-5 text-primary" />
                          </motion.div>
                        </div>

                        {/* Content */}
                        <div className="space-y-4">
                          <h3 className="text-2xl font-bold group-hover:text-primary transition-colors">
                            {step.title}
                          </h3>
                          <p className="text-foreground-muted leading-relaxed">
                            {step.description}
                          </p>
                        </div>

                        {/* Hover Glow */}
                        <div className={`absolute inset-0 rounded-3xl bg-gradient-to-r ${step.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-300`} />
                      </motion.div>
                    </MagneticElement>
                  </div>
                </FadeInUp>
              ))}
            </div>

            {/* Call to Action */}
            <FadeInUp delay={0.6} className="text-center mt-16">
              <MagneticElement>
                <Button
                  size="xl"
                  asChild
                  href={ROUTES.REGISTER}
                  gradient
                  glow
                  className="group"
                >
                  <Sparkles className="mr-2 h-6 w-6 group-hover:animate-spin" />
                  Start Your Success Story
                  <ArrowRight className="ml-2 h-6 w-6 group-hover:translate-x-2 transition-transform" />
                </Button>
              </MagneticElement>
            </FadeInUp>
          </StaggerContainer>
        </div>
      </section>

      {/* Success Stories - Reimagined */}
      <section className="py-32 relative overflow-hidden">
        {/* Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-accent/5 via-background to-primary/5" />
        <ParallaxElement speed={-0.2} className="absolute inset-0 opacity-20">
          <div className="absolute top-40 left-40 w-80 h-80 bg-gradient-to-r from-success/30 to-primary/30 rounded-full blur-3xl" />
          <div className="absolute bottom-40 right-40 w-96 h-96 bg-gradient-to-r from-accent/30 to-warning/30 rounded-full blur-3xl" />
        </ParallaxElement>

        <div className="container mx-auto px-4 relative z-10">
          <StaggerContainer>
            <FadeInUp className="text-center space-y-6 mb-20">
              <motion.div
                className="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-gradient-to-r from-success/10 to-primary/10 border border-success/20"
                whileHover={{ scale: 1.05 }}
              >
                <Star className="h-5 w-5 text-success" />
                <span className="font-medium text-success">Success Stories</span>
              </motion.div>

              <h2 className="text-4xl lg:text-6xl font-bold tracking-tight">
                Join{' '}
                <span className="gradient-text bg-gradient-to-r from-success via-primary to-accent bg-[length:200%_200%] animate-gradient">
                  50,000+ Professionals
                </span>
                <br />Who Got Hired
              </h2>

              <p className="text-xl lg:text-2xl text-foreground-muted max-w-3xl mx-auto leading-relaxed">
                Real people, real results. See how our platform transformed careers worldwide.
              </p>
            </FadeInUp>

            <div className="grid md:grid-cols-3 gap-8 mt-20">
              {[
                {
                  name: "Sarah Ahmed",
                  role: "Marketing Director",
                  company: "Tech Startup",
                  image: "SA",
                  quote: "Landed my dream job in 2 weeks! The AI suggestions were spot-on and the templates are absolutely stunning.",
                  rating: 5,
                  gradient: "from-primary to-accent",
                  delay: 0.1
                },
                {
                  name: "Michael Chen",
                  role: "Software Engineer",
                  company: "Fortune 500",
                  image: "MC",
                  quote: "The multi-language support helped me apply globally. Got offers from 3 different countries!",
                  rating: 5,
                  gradient: "from-accent to-success",
                  delay: 0.2
                },
                {
                  name: "Fatima Al-Rashid",
                  role: "Business Analyst",
                  company: "Consulting Firm",
                  image: "FA",
                  quote: "Perfect Arabic formatting and cultural considerations. Finally, a CV builder that understands diversity!",
                  rating: 5,
                  gradient: "from-success to-warning",
                  delay: 0.3
                }
              ].map((testimonial, index) => (
                <FadeInUp key={index} delay={testimonial.delay}>
                  <MagneticElement>
                    <motion.div
                      className="relative group bg-background-elevated rounded-3xl p-8 border border-border hover-lift overflow-hidden"
                      whileHover={{
                        y: -10,
                        rotateY: 5,
                        scale: 1.02
                      }}
                      transition={{ type: "spring", stiffness: 300, damping: 30 }}
                    >
                      {/* Background Glow */}
                      <div className={`absolute inset-0 bg-gradient-to-r ${testimonial.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-500 rounded-3xl`} />

                      {/* Stars */}
                      <div className="flex items-center gap-1 mb-6">
                        {[...Array(testimonial.rating)].map((_, i) => (
                          <motion.div
                            key={i}
                            initial={{ scale: 0, rotate: -180 }}
                            animate={{ scale: 1, rotate: 0 }}
                            transition={{ delay: 0.5 + i * 0.1, type: "spring" }}
                          >
                            <Star className="h-5 w-5 fill-warning text-warning" />
                          </motion.div>
                        ))}
                      </div>

                      {/* Quote */}
                      <blockquote className="text-lg leading-relaxed mb-8 relative">
                        <span className="text-4xl text-primary/20 absolute -top-2 -left-2">"</span>
                        {testimonial.quote}
                        <span className="text-4xl text-primary/20 absolute -bottom-4 -right-2">"</span>
                      </blockquote>

                      {/* Author */}
                      <div className="flex items-center gap-4">
                        <motion.div
                          className={`w-14 h-14 rounded-full bg-gradient-to-r ${testimonial.gradient} flex items-center justify-center text-white font-bold text-lg shadow-lg`}
                          whileHover={{ scale: 1.1, rotate: 5 }}
                        >
                          {testimonial.image}
                        </motion.div>
                        <div>
                          <p className="font-bold text-lg">{testimonial.name}</p>
                          <p className="text-foreground-muted">{testimonial.role}</p>
                          <p className="text-sm text-primary font-medium">{testimonial.company}</p>
                        </div>
                      </div>

                      {/* Shimmer Effect */}
                      <div className="absolute inset-0 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />
                      </div>
                    </motion.div>
                  </MagneticElement>
                </FadeInUp>
              ))}
            </div>
          </StaggerContainer>
        </div>
      </section>

      {/* Final CTA - Epic */}
      <section className="py-32 relative overflow-hidden">
        {/* Animated Background */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-primary via-accent to-success"
          animate={{
            background: [
              'linear-gradient(45deg, #0ea5e9, #8b5cf6, #10b981)',
              'linear-gradient(45deg, #8b5cf6, #10b981, #0ea5e9)',
              'linear-gradient(45deg, #10b981, #0ea5e9, #8b5cf6)',
              'linear-gradient(45deg, #0ea5e9, #8b5cf6, #10b981)',
            ]
          }}
          transition={{ duration: 8, repeat: Infinity }}
        />
        <div className="absolute inset-0 bg-black/20" />

        {/* Floating Elements */}
        <FloatingElement className="absolute top-20 left-20 opacity-30" intensity={20} duration={6}>
          <div className="w-32 h-32 rounded-full bg-white/10 blur-xl" />
        </FloatingElement>
        <FloatingElement className="absolute bottom-20 right-20 opacity-30" intensity={25} duration={8}>
          <div className="w-40 h-40 rounded-full bg-white/10 blur-2xl" />
        </FloatingElement>

        <div className="container mx-auto px-4 relative z-10">
          <StaggerContainer>
            <FadeInUp className="text-center space-y-8 max-w-4xl mx-auto">
              <motion.div
                className="inline-flex items-center gap-2 px-6 py-3 rounded-full glass border border-white/20 text-white"
                whileHover={{ scale: 1.05 }}
              >
                <Rocket className="h-5 w-5" />
                <span className="font-medium">Your Career Awaits</span>
              </motion.div>

              <h2 className="text-5xl lg:text-7xl font-bold tracking-tight text-white leading-tight">
                Ready to Transform
                <br />
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-white via-yellow-200 to-white bg-[length:200%_200%] animate-gradient">
                  Your Future?
                </span>
              </h2>

              <p className="text-xl lg:text-2xl text-white/80 max-w-3xl mx-auto leading-relaxed">
                Join thousands of professionals who've already discovered the power of AI-driven CV creation.
                Your dream job is just one click away.
              </p>

              <div className="flex flex-col sm:flex-row gap-6 justify-center pt-8">
                <MagneticElement>
                  <Button
                    size="xl"
                    asChild
                    href={ROUTES.REGISTER}
                    className="bg-white text-primary hover:bg-white/90 shadow-2xl group border-0"
                  >
                    <Sparkles className="mr-2 h-6 w-6 group-hover:animate-spin" />
                    Start Your Success Story
                    <ArrowRight className="ml-2 h-6 w-6 group-hover:translate-x-2 transition-transform" />
                  </Button>
                </MagneticElement>

                <MagneticElement>
                  <Button
                    variant="glass"
                    size="xl"
                    asChild
                    href="#preview"
                    className="text-white border-white/20 hover:bg-white/10 group"
                  >
                    <FileText className="mr-2 h-6 w-6" />
                    View Examples
                  </Button>
                </MagneticElement>
              </div>

              <motion.div
                className="flex items-center justify-center gap-8 pt-8 text-white/60"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 1 }}
              >
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-success" />
                  <span>Forever Free</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-success" />
                  <span>No Credit Card</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-success" />
                  <span>Instant Results</span>
                </div>
              </motion.div>
            </FadeInUp>
          </StaggerContainer>
        </div>
      </section>
    </MainLayout>
  );
}
