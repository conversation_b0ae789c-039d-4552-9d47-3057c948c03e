'use client';

import React from 'react';
import Link from 'next/link';
import { ArrowRight, CheckCircle, Star, Zap, Globe, Briefcase, FileText, Download } from 'lucide-react';
import { MainLayout } from '@/components/layout/MainLayout';
import { Button } from '@/components/ui/Button';
import { ROUTES } from '@/lib/constants';

export default function Home() {
  return (
    <MainLayout>
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-accent/5">
        <div className="container mx-auto px-4 py-20 lg:py-32">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <h1 className="text-4xl lg:text-6xl font-bold tracking-tight">
                  Craft Your Future.{' '}
                  <span className="text-primary">Build Your Perfect CV.</span>
                </h1>
                <p className="text-xl text-muted max-w-lg">
                  Create stunning, professional CVs with our intuitive builder.
                  Choose from beautiful templates, support for multiple languages,
                  and export to PDF instantly.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button size="lg" asChild>
                  <Link href={ROUTES.REGISTER}>
                    Create Your Free CV
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
                <Button variant="outline" size="lg" asChild>
                  <Link href="#how-it-works">
                    See How It Works
                  </Link>
                </Button>
              </div>

              <div className="flex items-center gap-6 text-sm text-muted">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>Free to start</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>No credit card required</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>Export to PDF</span>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="bg-white rounded-2xl shadow-2xl p-8 transform rotate-2 hover:rotate-0 transition-transform duration-300">
                <div className="space-y-4">
                  <div className="h-4 bg-primary rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  <div className="space-y-2">
                    <div className="h-2 bg-gray-100 rounded"></div>
                    <div className="h-2 bg-gray-100 rounded w-5/6"></div>
                    <div className="h-2 bg-gray-100 rounded w-4/6"></div>
                  </div>
                  <div className="pt-4 space-y-2">
                    <div className="h-3 bg-accent rounded w-2/3"></div>
                    <div className="h-2 bg-gray-100 rounded"></div>
                    <div className="h-2 bg-gray-100 rounded w-3/4"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Feature Highlights */}
      <section id="features" className="py-20 bg-secondary/30">
        <div className="container mx-auto px-4">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold">
              Everything You Need to Build a Winning CV
            </h2>
            <p className="text-xl text-muted max-w-2xl mx-auto">
              Our powerful features make CV creation effortless and professional
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-background rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
              <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                <FileText className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Professional Templates</h3>
              <p className="text-muted">
                Choose from beautifully designed templates optimized for different industries and regions.
              </p>
            </div>

            <div className="bg-background rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
              <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                <Globe className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Multi-Language Support</h3>
              <p className="text-muted">
                Create CVs in English, German, or Arabic with proper formatting and cultural considerations.
              </p>
            </div>

            <div className="bg-background rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
              <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                <Download className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Instant PDF Export</h3>
              <p className="text-muted">
                Download your CV as a high-quality PDF ready for job applications and printing.
              </p>
            </div>

            <div className="bg-background rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
              <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                <Zap className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Easy Editing</h3>
              <p className="text-muted">
                Intuitive section-by-section editing with real-time preview and auto-save functionality.
              </p>
            </div>

            <div className="bg-background rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
              <div className="h-12 w-12 bg-accent/10 rounded-lg flex items-center justify-center mb-4">
                <Briefcase className="h-6 w-6 text-accent" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Job Applications</h3>
              <p className="text-muted">
                <span className="inline-flex items-center gap-1">
                  Apply directly to jobs via email
                  <span className="text-xs bg-accent text-accent-foreground px-2 py-1 rounded">Coming Soon</span>
                </span>
              </p>
            </div>

            <div className="bg-background rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
              <div className="h-12 w-12 bg-accent/10 rounded-lg flex items-center justify-center mb-4">
                <Star className="h-6 w-6 text-accent" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Smart Job Matching</h3>
              <p className="text-muted">
                <span className="inline-flex items-center gap-1">
                  AI-powered job scraping and applications
                  <span className="text-xs bg-accent text-accent-foreground px-2 py-1 rounded">Coming Soon</span>
                </span>
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section id="how-it-works" className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold">
              Create Your CV in 3 Simple Steps
            </h2>
            <p className="text-xl text-muted max-w-2xl mx-auto">
              Our streamlined process gets you from idea to professional CV in minutes
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center space-y-4">
              <div className="h-16 w-16 bg-primary rounded-full flex items-center justify-center mx-auto text-primary-foreground text-2xl font-bold">
                1
              </div>
              <h3 className="text-xl font-semibold">Choose Template</h3>
              <p className="text-muted">
                Select from our collection of professional templates designed for different industries and regions.
              </p>
            </div>

            <div className="text-center space-y-4">
              <div className="h-16 w-16 bg-primary rounded-full flex items-center justify-center mx-auto text-primary-foreground text-2xl font-bold">
                2
              </div>
              <h3 className="text-xl font-semibold">Add Your Details</h3>
              <p className="text-muted">
                Fill in your information section by section with our intuitive forms and real-time preview.
              </p>
            </div>

            <div className="text-center space-y-4">
              <div className="h-16 w-16 bg-primary rounded-full flex items-center justify-center mx-auto text-primary-foreground text-2xl font-bold">
                3
              </div>
              <h3 className="text-xl font-semibold">Download & Share</h3>
              <p className="text-muted">
                Export your CV as a PDF and start applying to your dream jobs immediately.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20 bg-secondary/30">
        <div className="container mx-auto px-4">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold">
              Trusted by Job Seekers Worldwide
            </h2>
            <p className="text-xl text-muted max-w-2xl mx-auto">
              See what our users say about their experience
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-background rounded-xl p-6 shadow-sm">
              <div className="flex items-center gap-1 mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                ))}
              </div>
              <p className="text-muted mb-4">
                "The templates are beautiful and the multi-language support helped me create CVs for different markets. Got my dream job!"
              </p>
              <div className="flex items-center gap-3">
                <div className="h-10 w-10 bg-primary/10 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium">SA</span>
                </div>
                <div>
                  <p className="font-medium">Sarah Ahmed</p>
                  <p className="text-sm text-muted">Marketing Manager</p>
                </div>
              </div>
            </div>

            <div className="bg-background rounded-xl p-6 shadow-sm">
              <div className="flex items-center gap-1 mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                ))}
              </div>
              <p className="text-muted mb-4">
                "Super easy to use! I created a professional German CV in minutes. The cultural formatting is perfect."
              </p>
              <div className="flex items-center gap-3">
                <div className="h-10 w-10 bg-primary/10 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium">MK</span>
                </div>
                <div>
                  <p className="font-medium">Michael Klein</p>
                  <p className="text-sm text-muted">Software Engineer</p>
                </div>
              </div>
            </div>

            <div className="bg-background rounded-xl p-6 shadow-sm">
              <div className="flex items-center gap-1 mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                ))}
              </div>
              <p className="text-muted mb-4">
                "The Arabic support is excellent! Finally a CV builder that understands RTL layout and cultural requirements."
              </p>
              <div className="flex items-center gap-3">
                <div className="h-10 w-10 bg-primary/10 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium">AH</span>
                </div>
                <div>
                  <p className="font-medium">Ahmed Hassan</p>
                  <p className="text-sm text-muted">Business Analyst</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-3xl mx-auto space-y-8">
            <h2 className="text-3xl lg:text-4xl font-bold">
              Ready to Build Your Professional CV?
            </h2>
            <p className="text-xl text-muted">
              Join thousands of job seekers who have successfully created their CVs with our platform.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild>
                <Link href={ROUTES.REGISTER}>
                  Get Started for Free
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href={ROUTES.LOGIN}>
                  Sign In
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </MainLayout>
  );
}
