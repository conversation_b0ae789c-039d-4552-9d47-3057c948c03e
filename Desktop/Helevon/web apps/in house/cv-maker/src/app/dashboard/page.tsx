'use client';

import React, { useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Plus, FileText, Search, Settings, Sparkles, Target, TrendingUp } from 'lucide-react';
import { motion } from 'framer-motion';
import { MainLayout } from '@/components/layout/MainLayout';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { PageLoading } from '@/components/ui/LoadingSpinner';
import { CVCard } from '@/components/cv/CVCard';
import {
  FadeInUp,
  FadeInScale,
  StaggerContainer,
  MagneticElement,
  FloatingElement
} from '@/components/motion/MotionComponents';
import { useAuthStore } from '@/stores/auth';
import { useCVStore } from '@/stores/cv';
import { useToast } from '@/components/ui/ToastNotification';
import { ROUTES } from '@/lib/constants';

export default function DashboardPage() {
  const router = useRouter();
  const { user, isAuthenticated, isLoading: authLoading } = useAuthStore();
  const { cvs, isLoading: cvsLoading, error, fetchCVs } = useCVStore();
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = React.useState('');

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push(ROUTES.LOGIN);
    }
  }, [isAuthenticated, authLoading, router]);

  // Fetch CVs on mount - Fixed infinite loop by removing function dependencies
  useEffect(() => {
    if (isAuthenticated) {
      fetchCVs().catch((error) => {
        console.error('Failed to load CVs:', error);
        // We'll handle the error in the UI instead of using toast here
      });
    }
  }, [isAuthenticated]); // Only depend on isAuthenticated

  // Filter CVs based on search query
  const filteredCVs = cvs.filter(cv =>
    cv.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    cv.template.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (authLoading) {
    return (
      <MainLayout>
        <PageLoading text="Loading your account..." />
      </MainLayout>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect
  }

  return (
    <MainLayout>
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-accent/5 py-20">
        {/* Floating Background Elements */}
        <FloatingElement className="absolute top-10 left-10 opacity-20" intensity={15} duration={6}>
          <div className="w-32 h-32 rounded-full bg-gradient-to-r from-primary to-accent blur-xl" />
        </FloatingElement>
        <FloatingElement className="absolute bottom-10 right-10 opacity-20" intensity={20} duration={8}>
          <div className="w-40 h-40 rounded-full bg-gradient-to-r from-accent to-success blur-2xl" />
        </FloatingElement>

        <div className="container mx-auto px-4 relative z-10">
          <StaggerContainer>
            {/* Welcome Header */}
            <FadeInUp className="text-center mb-12">
              <motion.div
                className="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-gradient-to-r from-success/10 to-primary/10 border border-success/20 mb-6"
                whileHover={{ scale: 1.05 }}
              >
                <Sparkles className="h-5 w-5 text-success" />
                <span className="font-medium text-success">Your CV Dashboard</span>
              </motion.div>

              <h1 className="text-4xl lg:text-6xl font-bold tracking-tight mb-4">
                Welcome back,{' '}
                <span className="gradient-text bg-gradient-to-r from-primary via-accent to-success bg-[length:200%_200%] animate-gradient">
                  {user?.name}!
                </span>
              </h1>

              <p className="text-xl text-foreground-muted max-w-2xl mx-auto leading-relaxed">
                Manage your professional CVs and take your career to the next level
              </p>
            </FadeInUp>

            {/* Quick Stats */}
            <FadeInUp delay={0.2} className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
              {[
                {
                  icon: FileText,
                  label: 'Total CVs',
                  value: cvs.length,
                  gradient: 'from-primary to-primary-dark',
                  description: 'Professional CVs created'
                },
                {
                  icon: TrendingUp,
                  label: 'Success Rate',
                  value: '98%',
                  gradient: 'from-success to-success-light',
                  description: 'Interview success rate'
                },
                {
                  icon: Target,
                  label: 'Applications',
                  value: 'Coming Soon',
                  gradient: 'from-accent to-accent-dark',
                  description: 'Direct job applications'
                }
              ].map((stat, index) => (
                <MagneticElement key={index}>
                  <motion.div
                    className="relative group bg-background-elevated rounded-3xl p-6 border border-border hover-lift overflow-hidden"
                    whileHover={{ y: -5, scale: 1.02 }}
                    transition={{ type: "spring", stiffness: 300, damping: 30 }}
                  >
                    <div className={`absolute inset-0 bg-gradient-to-r ${stat.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-500 rounded-3xl`} />

                    <div className="relative z-10">
                      <div className={`inline-flex p-3 rounded-2xl bg-gradient-to-r ${stat.gradient} shadow-lg mb-4`}>
                        <stat.icon className="h-6 w-6 text-white" />
                      </div>

                      <div className="space-y-2">
                        <p className="text-sm font-medium text-foreground-muted">{stat.label}</p>
                        <p className="text-2xl font-bold">{stat.value}</p>
                        <p className="text-xs text-foreground-muted">{stat.description}</p>
                      </div>
                    </div>
                  </motion.div>
                </MagneticElement>
              ))}
            </FadeInUp>

            {/* Create CV CTA */}
            <FadeInUp delay={0.4} className="text-center">
              <MagneticElement>
                <Button
                  onClick={() => router.push(ROUTES.CV_CREATE)}
                  size="xl"
                  gradient
                  glow
                  className="group"
                >
                  <Plus className="h-6 w-6 mr-2 group-hover:rotate-90 transition-transform" />
                  Create New CV
                  <Sparkles className="h-6 w-6 ml-2 group-hover:animate-pulse" />
                </Button>
              </MagneticElement>
            </FadeInUp>
          </StaggerContainer>
        </div>
      </section>

      {/* CV Management Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">

          <StaggerContainer>
            {/* Section Header */}
            <FadeInUp className="text-center mb-12">
              <h2 className="text-3xl lg:text-4xl font-bold mb-4">
                Your CV{' '}
                <span className="gradient-text bg-gradient-to-r from-primary to-accent bg-[length:200%_200%] animate-gradient">
                  Collection
                </span>
              </h2>
              <p className="text-lg text-foreground-muted max-w-2xl mx-auto">
                Manage, edit, and export your professional CVs
              </p>
            </FadeInUp>

            {/* Search and Filters */}
            <FadeInUp delay={0.1} className="max-w-2xl mx-auto mb-12">
              <div className="relative group">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-foreground-muted group-focus-within:text-primary transition-colors" />
                <motion.div
                  whileFocus={{ scale: 1.02 }}
                  transition={{ type: "spring", stiffness: 300, damping: 30 }}
                >
                  <Input
                    placeholder="Search your CVs by title, template, or language..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-12 h-14 text-lg rounded-2xl border-2 border-border focus:border-primary/50 bg-background-elevated shadow-lg"
                  />
                </motion.div>

                {/* Search Results Count */}
                {searchQuery && (
                  <motion.div
                    className="absolute -bottom-8 left-0 text-sm text-foreground-muted"
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                  >
                    {filteredCVs.length} result{filteredCVs.length !== 1 ? 's' : ''} found
                  </motion.div>
                )}
              </div>
            </FadeInUp>

            {/* Content */}
            {cvsLoading ? (
              <div className="flex items-center justify-center py-20">
                <motion.div
                  className="text-center space-y-6"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5 }}
                >
                  <motion.div
                    className="w-16 h-16 mx-auto rounded-full bg-gradient-to-r from-primary to-accent flex items-center justify-center"
                    animate={{ rotate: 360 }}
                    transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                  >
                    <FileText className="h-8 w-8 text-white" />
                  </motion.div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2">Loading your CVs...</h3>
                    <p className="text-foreground-muted">Preparing your professional collection</p>
                  </div>
                </motion.div>
              </div>
            ) : error ? (
              <FadeInScale className="text-center py-20">
                <motion.div
                  className="inline-block p-8 rounded-3xl bg-destructive/5 border border-destructive/20"
                  whileHover={{ scale: 1.02 }}
                >
                  <div className="w-16 h-16 mx-auto mb-6 rounded-full bg-destructive/10 flex items-center justify-center">
                    <FileText className="h-8 w-8 text-destructive" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2 text-destructive">Something went wrong</h3>
                  <p className="text-foreground-muted mb-6">{error}</p>
                  <Button
                    variant="outline"
                    onClick={() => window.location.reload()}
                    className="border-destructive/20 hover:bg-destructive/5"
                  >
                    Try Again
                  </Button>
                </motion.div>
              </FadeInScale>
            ) : filteredCVs.length === 0 ? (
              searchQuery ? (
                // No search results
                <FadeInScale className="text-center py-20">
                  <motion.div
                    className="max-w-md mx-auto"
                    whileHover={{ scale: 1.02 }}
                  >
                    <div className="w-20 h-20 mx-auto mb-6 rounded-full bg-gradient-to-r from-accent/10 to-primary/10 flex items-center justify-center">
                      <Search className="h-10 w-10 text-accent" />
                    </div>
                    <h3 className="text-2xl font-bold mb-4">No CVs found</h3>
                    <p className="text-foreground-muted mb-8 leading-relaxed">
                      No CVs match your search for <span className="font-medium text-primary">"{searchQuery}"</span>.
                      Try a different search term or create a new CV.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                      <Button
                        variant="outline"
                        onClick={() => setSearchQuery('')}
                      >
                        Clear Search
                      </Button>
                      <Button
                        onClick={() => router.push(ROUTES.CV_CREATE)}
                        gradient
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Create New CV
                      </Button>
                    </div>
                  </motion.div>
                </FadeInScale>
              ) : (
                // Empty state
                <FadeInScale className="text-center py-20">
                  <motion.div
                    className="max-w-2xl mx-auto"
                    whileHover={{ scale: 1.02 }}
                  >
                    <FloatingElement intensity={8} duration={4}>
                      <div className="w-32 h-32 mx-auto mb-8 rounded-full bg-gradient-to-r from-primary/10 to-accent/10 flex items-center justify-center relative overflow-hidden">
                        <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-accent/20 animate-pulse" />
                        <FileText className="h-16 w-16 text-primary relative z-10" />
                      </div>
                    </FloatingElement>

                    <h3 className="text-3xl font-bold mb-4">
                      Ready to Create Your{' '}
                      <span className="gradient-text bg-gradient-to-r from-primary to-accent bg-[length:200%_200%] animate-gradient">
                        First Masterpiece?
                      </span>
                    </h3>

                    <p className="text-xl text-foreground-muted mb-8 leading-relaxed">
                      Transform your career story into a compelling CV that opens doors to your dream opportunities.
                    </p>

                    <MagneticElement>
                      <Button
                        onClick={() => router.push(ROUTES.CV_CREATE)}
                        size="xl"
                        gradient
                        glow
                        className="group"
                      >
                        <Sparkles className="h-6 w-6 mr-2 group-hover:animate-spin" />
                        Create Your First CV
                        <Plus className="h-6 w-6 ml-2 group-hover:rotate-90 transition-transform" />
                      </Button>
                    </MagneticElement>
                  </motion.div>
                </FadeInScale>
              )
            ) : (
              // CV Grid
              <div className="space-y-8">
                <FadeInUp delay={0.2}>
                  <div className="flex justify-between items-center">
                    <motion.p
                      className="text-lg font-medium"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                    >
                      {filteredCVs.length} {filteredCVs.length === 1 ? 'CV' : 'CVs'}
                      {searchQuery && (
                        <span className="text-foreground-muted">
                          {' '}matching <span className="text-primary font-semibold">"{searchQuery}"</span>
                        </span>
                      )}
                    </motion.p>
                  </div>
                </FadeInUp>

                <StaggerContainer>
                  <motion.div
                    className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
                    variants={{
                      animate: {
                        transition: {
                          staggerChildren: 0.1
                        }
                      }
                    }}
                  >
                    {filteredCVs.map((cv, index) => (
                      <FadeInUp key={cv.id} delay={index * 0.1}>
                        <CVCard cv={cv} />
                      </FadeInUp>
                    ))}
                  </motion.div>
                </StaggerContainer>
              </div>
            )}

          </StaggerContainer>
        </div>
      </section>

      {/* Quick Actions Section */}
      {cvs.length > 0 && (
        <section className="py-16 bg-gradient-to-r from-secondary/30 via-background to-secondary/30">
          <div className="container mx-auto px-4">
            <StaggerContainer>
              <FadeInUp className="text-center mb-12">
                <h2 className="text-3xl font-bold mb-4">
                  Quick{' '}
                  <span className="gradient-text bg-gradient-to-r from-accent to-primary bg-[length:200%_200%] animate-gradient">
                    Actions
                  </span>
                </h2>
                <p className="text-lg text-foreground-muted">
                  Everything you need to manage your professional presence
                </p>
              </FadeInUp>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {[
                  {
                    icon: Plus,
                    title: 'Create New CV',
                    description: 'Start fresh with our AI-powered CV builder',
                    action: () => router.push(ROUTES.CV_CREATE),
                    gradient: 'from-primary to-primary-dark',
                    delay: 0.1
                  },
                  {
                    icon: FileText,
                    title: 'Browse Templates',
                    description: 'Explore our premium template collection',
                    action: () => router.push('/templates'),
                    gradient: 'from-accent to-accent-dark',
                    delay: 0.2
                  },
                  {
                    icon: Settings,
                    title: 'Account Settings',
                    description: 'Manage your profile and preferences',
                    action: () => router.push(ROUTES.PROFILE),
                    gradient: 'from-success to-success-light',
                    delay: 0.3
                  }
                ].map((action, index) => (
                  <FadeInUp key={index} delay={action.delay}>
                    <MagneticElement>
                      <motion.button
                        onClick={action.action}
                        className="w-full group relative bg-background-elevated rounded-3xl p-8 border border-border hover-lift overflow-hidden text-left"
                        whileHover={{
                          y: -8,
                          scale: 1.02,
                          boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)"
                        }}
                        whileTap={{ scale: 0.98 }}
                        transition={{ type: "spring", stiffness: 300, damping: 30 }}
                      >
                        {/* Background Glow */}
                        <div className={`absolute inset-0 bg-gradient-to-r ${action.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-500 rounded-3xl`} />

                        {/* Icon */}
                        <div className={`inline-flex p-4 rounded-2xl bg-gradient-to-r ${action.gradient} shadow-lg mb-6 group-hover:scale-110 transition-transform duration-300`}>
                          <action.icon className="h-8 w-8 text-white" />
                        </div>

                        {/* Content */}
                        <div className="relative z-10 space-y-3">
                          <h3 className="text-xl font-bold group-hover:text-primary transition-colors">
                            {action.title}
                          </h3>
                          <p className="text-foreground-muted leading-relaxed">
                            {action.description}
                          </p>
                        </div>

                        {/* Hover Effect */}
                        <div className="absolute inset-0 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />
                        </div>
                      </motion.button>
                    </MagneticElement>
                  </FadeInUp>
                ))}
              </div>
            </StaggerContainer>
          </div>
        </section>
      )}
    </MainLayout>
  );
}
