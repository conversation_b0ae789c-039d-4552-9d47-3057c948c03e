'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Plus, FileText, Search, Settings } from 'lucide-react';
import { MainLayout } from '@/components/layout/MainLayout';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { PageLoading } from '@/components/ui/LoadingSpinner';
import { CVCard } from '@/components/cv/CVCard';
import { useAuthStore } from '@/stores/auth';
import { useCVStore } from '@/stores/cv';
import { useToast } from '@/components/ui/ToastNotification';
import { ROUTES } from '@/lib/constants';

export default function DashboardPage() {
  const router = useRouter();
  const { user, isAuthenticated, isLoading: authLoading } = useAuthStore();
  const { cvs, isLoading: cvsLoading, error, fetchCVs } = useCVStore();
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = React.useState('');

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push(ROUTES.LOGIN);
    }
  }, [isAuthenticated, authLoading, router]);

  // Fetch CVs on mount
  useEffect(() => {
    if (isAuthenticated) {
      fetchCVs().catch((error) => {
        toast.error('Failed to load CVs', error.message);
      });
    }
  }, [isAuthenticated, fetchCVs, toast]);

  // Filter CVs based on search query
  const filteredCVs = cvs.filter(cv =>
    cv.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    cv.template.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (authLoading) {
    return (
      <MainLayout>
        <PageLoading text="Loading your account..." />
      </MainLayout>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
          <div>
            <h1 className="text-3xl font-bold">My CVs</h1>
            <p className="text-muted mt-1">
              Welcome back, {user?.name}! Manage your professional CVs here.
            </p>
          </div>
          
          <Button
            onClick={() => router.push(ROUTES.CV_CREATE)}
            size="lg"
          >
            <Plus className="h-5 w-5 mr-2" />
            Create New CV
          </Button>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-8">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted" />
            <Input
              placeholder="Search your CVs..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          {/* TODO: Add filter dropdown for template type, language, etc. */}
        </div>

        {/* Content */}
        {cvsLoading ? (
          <PageLoading text="Loading your CVs..." />
        ) : error ? (
          <div className="text-center py-12">
            <div className="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-md inline-block">
              {error}
            </div>
          </div>
        ) : filteredCVs.length === 0 ? (
          searchQuery ? (
            // No search results
            <div className="text-center py-12">
              <Search className="h-12 w-12 text-muted mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">No CVs found</h3>
              <p className="text-muted mb-6">
                No CVs match your search for "{searchQuery}". Try a different search term.
              </p>
              <Button
                variant="outline"
                onClick={() => setSearchQuery('')}
              >
                Clear Search
              </Button>
            </div>
          ) : (
            // Empty state
            <div className="text-center py-12">
              <div className="bg-secondary/30 rounded-full h-24 w-24 flex items-center justify-center mx-auto mb-6">
                <FileText className="h-12 w-12 text-muted" />
              </div>
              <h3 className="text-2xl font-semibold mb-2">You haven't created any CVs yet!</h3>
              <p className="text-muted mb-8 max-w-md mx-auto">
                Get started by creating your first professional CV. Choose from our beautiful templates and build your career profile.
              </p>
              <Button
                onClick={() => router.push(ROUTES.CV_CREATE)}
                size="lg"
              >
                <Plus className="h-5 w-5 mr-2" />
                Create Your First CV
              </Button>
            </div>
          )
        ) : (
          // CV Grid
          <div>
            <div className="flex justify-between items-center mb-6">
              <p className="text-muted">
                {filteredCVs.length} {filteredCVs.length === 1 ? 'CV' : 'CVs'} found
                {searchQuery && ` for "${searchQuery}"`}
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredCVs.map((cv) => (
                <CVCard key={cv.id} cv={cv} />
              ))}
            </div>
          </div>
        )}

        {/* Quick Actions */}
        {cvs.length > 0 && (
          <div className="mt-12 pt-8 border-t border-border">
            <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              <Button
                variant="outline"
                onClick={() => router.push(ROUTES.CV_CREATE)}
                className="h-auto p-4 flex flex-col items-center gap-2"
              >
                <Plus className="h-6 w-6" />
                <span>Create New CV</span>
              </Button>
              
              <Button
                variant="outline"
                onClick={() => router.push('/templates')}
                className="h-auto p-4 flex flex-col items-center gap-2"
              >
                <FileText className="h-6 w-6" />
                <span>Browse Templates</span>
              </Button>
              
              <Button
                variant="outline"
                onClick={() => router.push(ROUTES.PROFILE)}
                className="h-auto p-4 flex flex-col items-center gap-2"
              >
                <Settings className="h-6 w-6" />
                <span>Account Settings</span>
              </Button>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
}
