'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  ArrowLeft, 
  Download, 
  Share2, 
  Edit, 
  Eye,
  Palette,
  Settings,
  FileText,
  Sparkles,
  Zap
} from 'lucide-react';
import { MainLayout } from '@/components/layout/MainLayout';
import { Button } from '@/components/ui/Button';
import { 
  FadeInUp, 
  FadeInScale, 
  MagneticElement,
  FloatingElement
} from '@/components/motion/MotionComponents';
import { useAuthStore } from '@/stores/auth';
import { useCVStore } from '@/stores/cv';
import { useToast } from '@/components/ui/ToastNotification';
import { ROUTES } from '@/lib/constants';

export default function CVPreviewPage() {
  const router = useRouter();
  const params = useParams();
  const { isAuthenticated } = useAuthStore();
  const { currentCV, isLoading, fetchCV } = useCVStore();
  const { toast } = useToast();
  
  const cvId = params.id as string;
  const [isExporting, setIsExporting] = useState(false);

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      router.push(ROUTES.LOGIN);
    }
  }, [isAuthenticated, router]);

  // Fetch CV data
  useEffect(() => {
    if (isAuthenticated && cvId) {
      fetchCV(cvId).catch((error) => {
        console.error('Failed to load CV:', error);
        toast.error('Failed to load CV', 'Please try again');
        router.push(ROUTES.DASHBOARD);
      });
    }
  }, [isAuthenticated, cvId]);

  const handleExport = async () => {
    setIsExporting(true);
    try {
      // TODO: Implement actual PDF export
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate export
      toast.success('CV exported successfully!', 'Your PDF is ready for download');
    } catch (error) {
      toast.error('Export failed', 'Please try again');
    } finally {
      setIsExporting(false);
    }
  };

  const handleEdit = () => {
    router.push(`/cv/${cvId}/edit`);
  };

  const handleShare = () => {
    // TODO: Implement sharing functionality
    toast.success('Share link copied!', 'Share your CV with potential employers');
  };

  if (!isAuthenticated) {
    return null;
  }

  if (isLoading) {
    return (
      <MainLayout>
        <div className="min-h-screen flex items-center justify-center">
          <motion.div
            className="text-center space-y-6"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            <motion.div
              className="w-16 h-16 mx-auto rounded-full bg-gradient-to-r from-primary to-accent flex items-center justify-center"
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            >
              <FileText className="h-8 w-8 text-white" />
            </motion.div>
            <div>
              <h3 className="text-xl font-semibold mb-2">Loading your CV...</h3>
              <p className="text-foreground-muted">Preparing your professional preview</p>
            </div>
          </motion.div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout showFooter={false}>
      {/* Header */}
      <div className="sticky top-16 z-40 bg-background/95 backdrop-blur border-b border-border">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={() => router.push(ROUTES.DASHBOARD)}
                className="group"
              >
                <ArrowLeft className="h-4 w-4 mr-2 group-hover:-translate-x-1 transition-transform" />
                Back to Dashboard
              </Button>
              
              <div className="hidden md:block">
                <h1 className="text-xl font-bold">CV Preview</h1>
                <p className="text-sm text-foreground-muted">
                  {currentCV?.title || 'Untitled CV'} • Ready to export
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                onClick={handleEdit}
                className="group"
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit CV
              </Button>
              
              <Button
                variant="outline"
                onClick={handleShare}
                className="group"
              >
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
              
              <MagneticElement>
                <Button
                  onClick={handleExport}
                  disabled={isExporting}
                  gradient
                  glow
                  className="group"
                >
                  {isExporting ? (
                    <>
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        className="mr-2"
                      >
                        <Download className="h-4 w-4" />
                      </motion.div>
                      Exporting...
                    </>
                  ) : (
                    <>
                      <Download className="h-4 w-4 mr-2" />
                      Export PDF
                      <Sparkles className="h-4 w-4 ml-2 group-hover:animate-pulse" />
                    </>
                  )}
                </Button>
              </MagneticElement>
            </div>
          </div>
        </div>
      </div>

      <div className="flex min-h-screen">
        {/* Customization Sidebar */}
        <div className="w-80 bg-secondary/30 border-r border-border p-6">
          <FadeInUp>
            <h3 className="text-lg font-semibold mb-6 flex items-center gap-2">
              <Palette className="h-5 w-5 text-primary" />
              Customization
            </h3>
          </FadeInUp>

          <div className="space-y-6">
            {/* Template Selection */}
            <FadeInUp delay={0.1}>
              <div className="bg-background-elevated rounded-xl p-4">
                <h4 className="font-medium mb-3">Template</h4>
                <div className="text-sm text-foreground-muted mb-3">
                  Current: {currentCV?.template || 'Modern Professional'}
                </div>
                <Button variant="outline" size="sm" className="w-full">
                  Change Template
                </Button>
              </div>
            </FadeInUp>

            {/* Color Customization */}
            <FadeInUp delay={0.2}>
              <div className="bg-background-elevated rounded-xl p-4">
                <h4 className="font-medium mb-3">Colors</h4>
                <div className="grid grid-cols-4 gap-2">
                  {[
                    '#0ea5e9', '#8b5cf6', '#10b981', '#f59e0b',
                    '#ef4444', '#6366f1', '#ec4899', '#14b8a6'
                  ].map((color, index) => (
                    <motion.button
                      key={color}
                      className="w-8 h-8 rounded-lg border-2 border-border hover:border-primary transition-colors"
                      style={{ backgroundColor: color }}
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    />
                  ))}
                </div>
              </div>
            </FadeInUp>

            {/* Export Options */}
            <FadeInUp delay={0.3}>
              <div className="bg-background-elevated rounded-xl p-4">
                <h4 className="font-medium mb-3">Export Options</h4>
                <div className="space-y-3">
                  <label className="flex items-center gap-2">
                    <input type="checkbox" defaultChecked className="rounded" />
                    <span className="text-sm">Include cover letter</span>
                  </label>
                  <label className="flex items-center gap-2">
                    <input type="checkbox" defaultChecked className="rounded" />
                    <span className="text-sm">Include references</span>
                  </label>
                  <label className="flex items-center gap-2">
                    <input type="checkbox" className="rounded" />
                    <span className="text-sm">Watermark</span>
                  </label>
                </div>
              </div>
            </FadeInUp>
          </div>
        </div>

        {/* CV Preview Area */}
        <div className="flex-1 p-8 bg-gradient-to-br from-secondary/10 to-background">
          <FadeInScale className="max-w-4xl mx-auto">
            {/* CV Preview Container */}
            <motion.div
              className="bg-white rounded-2xl shadow-2xl overflow-hidden border border-border"
              whileHover={{ y: -4 }}
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
            >
              {/* Mock CV Content */}
              <div className="aspect-[8.5/11] p-12 relative">
                {/* Floating Elements for Visual Interest */}
                <FloatingElement className="absolute top-4 right-4 opacity-10" intensity={5} duration={8}>
                  <div className="w-16 h-16 rounded-full bg-gradient-to-r from-primary to-accent" />
                </FloatingElement>

                {/* Header */}
                <div className="text-center mb-8">
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">
                    {currentCV?.personal_info?.first_name || 'John'} {currentCV?.personal_info?.last_name || 'Doe'}
                  </h1>
                  <p className="text-lg text-gray-600 mb-4">Professional Title</p>
                  <div className="flex justify-center gap-6 text-sm text-gray-500">
                    <span><EMAIL></span>
                    <span>+1 (555) 123-4567</span>
                    <span>City, Country</span>
                  </div>
                </div>

                {/* Professional Summary */}
                <div className="mb-6">
                  <h2 className="text-xl font-bold text-gray-900 mb-3 border-b-2 border-primary pb-1">
                    Professional Summary
                  </h2>
                  <p className="text-gray-700 leading-relaxed">
                    Experienced professional with a proven track record of success in delivering high-quality results. 
                    Passionate about innovation and continuous learning, with strong analytical and problem-solving skills.
                  </p>
                </div>

                {/* Experience */}
                <div className="mb-6">
                  <h2 className="text-xl font-bold text-gray-900 mb-3 border-b-2 border-primary pb-1">
                    Work Experience
                  </h2>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h3 className="font-semibold text-gray-900">Senior Position</h3>
                          <p className="text-gray-600">Company Name</p>
                        </div>
                        <span className="text-sm text-gray-500">2020 - Present</span>
                      </div>
                      <ul className="text-gray-700 text-sm space-y-1 ml-4">
                        <li>• Led cross-functional teams to deliver complex projects</li>
                        <li>• Improved efficiency by 30% through process optimization</li>
                        <li>• Mentored junior team members and facilitated knowledge sharing</li>
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Education */}
                <div className="mb-6">
                  <h2 className="text-xl font-bold text-gray-900 mb-3 border-b-2 border-primary pb-1">
                    Education
                  </h2>
                  <div>
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-semibold text-gray-900">Master's Degree</h3>
                        <p className="text-gray-600">University Name</p>
                      </div>
                      <span className="text-sm text-gray-500">2018</span>
                    </div>
                  </div>
                </div>

                {/* Skills */}
                <div>
                  <h2 className="text-xl font-bold text-gray-900 mb-3 border-b-2 border-primary pb-1">
                    Skills
                  </h2>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Technical Skills</h4>
                      <div className="flex flex-wrap gap-2">
                        {['JavaScript', 'React', 'Node.js', 'Python'].map(skill => (
                          <span key={skill} className="px-2 py-1 bg-primary/10 text-primary text-xs rounded">
                            {skill}
                          </span>
                        ))}
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Soft Skills</h4>
                      <div className="flex flex-wrap gap-2">
                        {['Leadership', 'Communication', 'Problem Solving'].map(skill => (
                          <span key={skill} className="px-2 py-1 bg-accent/10 text-accent text-xs rounded">
                            {skill}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </FadeInScale>
        </div>
      </div>
    </MainLayout>
  );
}
