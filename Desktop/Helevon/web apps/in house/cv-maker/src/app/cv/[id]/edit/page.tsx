'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  User, 
  GraduationCap, 
  Briefcase, 
  Award, 
  Users, 
  FileText,
  Save,
  Eye,
  ArrowLeft,
  ArrowRight,
  CheckCircle,
  Sparkles,
  Target,
  Zap
} from 'lucide-react';
import { MainLayout } from '@/components/layout/MainLayout';
import { Button } from '@/components/ui/Button';
import { 
  FadeInUp, 
  FadeInScale, 
  StaggerContainer,
  MagneticElement,
  FloatingElement
} from '@/components/motion/MotionComponents';
import { useAuthStore } from '@/stores/auth';
import { useCVStore } from '@/stores/cv';
import { useToast } from '@/components/ui/ToastNotification';
import { ROUTES } from '@/lib/constants';

// CV Section Components
import { PersonalInfoSection } from '@/components/cv/sections/PersonalInfoSection';
import { EducationSection } from '@/components/cv/sections/EducationSection';
import { ExperienceSection } from '@/components/cv/sections/ExperienceSection';
import { SkillsSection } from '@/components/cv/sections/SkillsSection';
import { ReferencesSection } from '@/components/cv/sections/ReferencesSection';
import { CoverLetterSection } from '@/components/cv/sections/CoverLetterSection';

type CVSection = 'personal' | 'education' | 'experience' | 'skills' | 'references' | 'cover-letter';

interface SectionConfig {
  id: CVSection;
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  component: React.ComponentType<any>;
  required: boolean;
}

const sections: SectionConfig[] = [
  {
    id: 'personal',
    title: 'Personal Information',
    icon: User,
    description: 'Basic details and contact information',
    component: PersonalInfoSection,
    required: true
  },
  {
    id: 'education',
    title: 'Education',
    icon: GraduationCap,
    description: 'Academic background and qualifications',
    component: EducationSection,
    required: false
  },
  {
    id: 'experience',
    title: 'Work Experience',
    icon: Briefcase,
    description: 'Professional experience and achievements',
    component: ExperienceSection,
    required: false
  },
  {
    id: 'skills',
    title: 'Skills',
    icon: Award,
    description: 'Technical and soft skills',
    component: SkillsSection,
    required: false
  },
  {
    id: 'references',
    title: 'References',
    icon: Users,
    description: 'Professional references',
    component: ReferencesSection,
    required: false
  },
  {
    id: 'cover-letter',
    title: 'Cover Letter',
    icon: FileText,
    description: 'Personalized cover letter',
    component: CoverLetterSection,
    required: false
  }
];

export default function CVEditPage() {
  const router = useRouter();
  const params = useParams();
  const { isAuthenticated } = useAuthStore();
  const { currentCV, isLoading, fetchCV } = useCVStore();
  const { toast } = useToast();

  const cvId = params.id as string;
  const [currentSection, setCurrentSection] = useState<CVSection>('personal');
  const [completedSections, setCompletedSections] = useState<Set<CVSection>>(new Set());

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      router.push(ROUTES.LOGIN);
    }
  }, [isAuthenticated, router]);

  // Fetch CV data and populate forms
  useEffect(() => {
    if (isAuthenticated && cvId) {
      fetchCV(cvId).then(() => {
        // Check which sections are already completed based on fetched data
        const cv = currentCV;
        if (cv) {
          const completed = new Set<CVSection>();

          // Check personal info
          if (cv.personal_info && Object.keys(cv.personal_info).length > 0) {
            completed.add('personal');
          }

          // Check education
          if (cv.education && cv.education.length > 0) {
            completed.add('education');
          }

          // Check work experience
          if (cv.work_experience && cv.work_experience.length > 0) {
            completed.add('experience');
          }

          // Check skills
          if (cv.skills && cv.skills.length > 0) {
            completed.add('skills');
          }

          // Check references
          if (cv.references && cv.references.length > 0) {
            completed.add('references');
          }

          // Check cover letter
          if (cv.cover_letter && cv.cover_letter.trim().length > 0) {
            completed.add('cover-letter');
          }

          setCompletedSections(completed);
        }
      }).catch((error) => {
        console.error('Failed to load CV:', error);
        toast.error('Failed to load CV', 'Please try again');
        router.push(ROUTES.DASHBOARD);
      });
    }
  }, [isAuthenticated, cvId, fetchCV, currentCV, toast, router]);

  const currentSectionIndex = sections.findIndex(s => s.id === currentSection);
  const currentSectionConfig = sections[currentSectionIndex];

  const handleSectionComplete = (sectionId: CVSection) => {
    setCompletedSections(prev => new Set([...prev, sectionId]));
  };

  const handleNextSection = () => {
    if (currentSectionIndex < sections.length - 1) {
      setCurrentSection(sections[currentSectionIndex + 1].id);
    }
  };

  const handlePreviousSection = () => {
    if (currentSectionIndex > 0) {
      setCurrentSection(sections[currentSectionIndex - 1].id);
    }
  };

  const handlePreview = () => {
    router.push(`/cv/${cvId}/preview`);
  };

  const progressPercentage = (completedSections.size / sections.length) * 100;

  if (!isAuthenticated) {
    return null;
  }

  if (isLoading) {
    return (
      <MainLayout>
        <div className="min-h-screen flex items-center justify-center">
          <motion.div
            className="text-center space-y-6"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            <motion.div
              className="w-16 h-16 mx-auto rounded-full bg-gradient-to-r from-primary to-accent flex items-center justify-center"
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            >
              <Sparkles className="h-8 w-8 text-white" />
            </motion.div>
            <div>
              <h3 className="text-xl font-semibold mb-2">Loading your CV...</h3>
              <p className="text-foreground-muted">Preparing your editor</p>
            </div>
          </motion.div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout showFooter={false}>
      {/* Header */}
      <div className="sticky top-16 z-40 bg-background/95 backdrop-blur border-b border-border">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={() => router.push(ROUTES.DASHBOARD)}
                className="group"
              >
                <ArrowLeft className="h-4 w-4 mr-2 group-hover:-translate-x-1 transition-transform" />
                Back to Dashboard
              </Button>
              
              <div className="hidden md:block">
                <h1 className="text-xl font-bold">Edit CV</h1>
                <p className="text-sm text-foreground-muted">
                  {currentCV?.title || 'Untitled CV'} • {currentSectionConfig.title} • {completedSections.size}/{sections.length} sections completed
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                onClick={handlePreview}
                className="group"
              >
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </Button>
              
              <Button
                variant="ghost"
                className="group"
              >
                <Save className="h-4 w-4 mr-2" />
                Auto-saved
              </Button>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="mt-4">
            <div className="flex justify-between text-xs text-foreground-muted mb-2">
              <span>Progress</span>
              <span>{Math.round(progressPercentage)}% complete</span>
            </div>
            <div className="w-full bg-secondary rounded-full h-2">
              <motion.div
                className="bg-gradient-to-r from-primary to-accent h-2 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${progressPercentage}%` }}
                transition={{ duration: 0.5 }}
              />
            </div>
          </div>
        </div>
      </div>

      <div className="flex min-h-screen">
        {/* Sidebar Navigation */}
        <div className="w-80 bg-secondary/30 border-r border-border p-6">
          <div className="space-y-2">
            {sections.map((section, index) => {
              const isCompleted = completedSections.has(section.id);
              const isCurrent = currentSection === section.id;
              const isAccessible = index === 0 || completedSections.has(sections[index - 1].id);

              return (
                <motion.button
                  key={section.id}
                  onClick={() => isAccessible && setCurrentSection(section.id)}
                  className={`w-full text-left p-4 rounded-xl transition-all duration-200 ${
                    isCurrent 
                      ? 'bg-primary text-white shadow-lg' 
                      : isCompleted
                      ? 'bg-success/10 text-success hover:bg-success/20'
                      : isAccessible
                      ? 'hover:bg-background-elevated'
                      : 'opacity-50 cursor-not-allowed'
                  }`}
                  whileHover={isAccessible ? { scale: 1.02, x: 4 } : undefined}
                  whileTap={isAccessible ? { scale: 0.98 } : undefined}
                  disabled={!isAccessible}
                >
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-lg ${
                      isCurrent 
                        ? 'bg-white/20' 
                        : isCompleted
                        ? 'bg-success/20'
                        : 'bg-secondary'
                    }`}>
                      {isCompleted ? (
                        <CheckCircle className="h-5 w-5" />
                      ) : (
                        <section.icon className="h-5 w-5" />
                      )}
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium">{section.title}</h3>
                        {section.required && (
                          <span className="text-xs bg-destructive/20 text-destructive px-2 py-0.5 rounded">
                            Required
                          </span>
                        )}
                      </div>
                      <p className={`text-xs mt-1 ${
                        isCurrent ? 'text-white/80' : 'text-foreground-muted'
                      }`}>
                        {section.description}
                      </p>
                    </div>
                  </div>
                </motion.button>
              );
            })}
          </div>

          {/* Quick Stats */}
          <div className="mt-8 p-4 bg-background-elevated rounded-xl">
            <h4 className="font-medium mb-3">CV Information</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-foreground-muted">Completed</span>
                <span className="font-medium">{completedSections.size}/{sections.length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-foreground-muted">Template</span>
                <span className="font-medium capitalize">{currentCV?.template || 'Modern'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-foreground-muted">Language</span>
                <span className="font-medium uppercase">{currentCV?.language || 'EN'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-foreground-muted">Last Updated</span>
                <span className="font-medium">
                  {currentCV?.updated_at ? new Date(currentCV.updated_at).toLocaleDateString() : 'Today'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 p-8">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentSection}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              className="max-w-4xl mx-auto"
            >
              {/* Section Header */}
              <div className="mb-8">
                <div className="flex items-center gap-3 mb-4">
                  <div className="p-3 bg-primary/10 rounded-xl">
                    <currentSectionConfig.icon className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold">{currentSectionConfig.title}</h2>
                    <p className="text-foreground-muted">{currentSectionConfig.description}</p>
                  </div>
                </div>
              </div>

              {/* Dynamic Section Component */}
              <div className="mb-8">
                <currentSectionConfig.component
                  cvId={cvId}
                  onComplete={() => handleSectionComplete(currentSection)}
                  isCompleted={completedSections.has(currentSection)}
                />
              </div>

              {/* Navigation */}
              <div className="flex justify-between items-center pt-8 border-t border-border">
                <Button
                  variant="outline"
                  onClick={handlePreviousSection}
                  disabled={currentSectionIndex === 0}
                  className="group"
                >
                  <ArrowLeft className="h-4 w-4 mr-2 group-hover:-translate-x-1 transition-transform" />
                  Previous
                </Button>

                <div className="flex items-center gap-3">
                  {currentSectionIndex === sections.length - 1 ? (
                    <MagneticElement>
                      <Button
                        onClick={handlePreview}
                        gradient
                        glow
                        size="lg"
                        className="group"
                      >
                        <Target className="h-5 w-5 mr-2" />
                        Preview & Export
                        <Zap className="h-5 w-5 ml-2 group-hover:animate-pulse" />
                      </Button>
                    </MagneticElement>
                  ) : (
                    <Button
                      onClick={handleNextSection}
                      className="group"
                    >
                      Next
                      <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  )}
                </div>
              </div>
            </motion.div>
          </AnimatePresence>
        </div>
      </div>
    </MainLayout>
  );
}
