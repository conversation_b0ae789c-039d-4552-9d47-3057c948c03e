'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { User, Shield, Globe, Eye, EyeOff, Calendar, Clock } from 'lucide-react';
import { MainLayout } from '@/components/layout/MainLayout';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Dropdown } from '@/components/ui/Dropdown';
import { PageLoading } from '@/components/ui/LoadingSpinner';
import { useAuthStore } from '@/stores/auth';
import { useToast } from '@/components/ui/ToastNotification';
import { ROUTES, SUPPORTED_LANGUAGES } from '@/lib/constants';
import { validateEmail, validatePassword, formatDateTime } from '@/lib/utils';
import type { UserUpdatePayload, UserProfile } from '@/types/auth';

export default function ProfilePage() {
  const router = useRouter();
  const { 
    user, 
    isAuthenticated, 
    isLoading: authLoading, 
    updateAccount, 
    fetchUserProfile 
  } = useAuthStore();
  const { toast } = useToast();

  const [activeTab, setActiveTab] = useState<'account' | 'security' | 'profile'>('account');
  const [isLoading, setIsLoading] = useState(false);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Account form state
  const [accountForm, setAccountForm] = useState({
    name: '',
    email: '',
    language: 'en' as 'en' | 'de' | 'ar',
  });

  // Security form state
  const [securityForm, setSecurityForm] = useState({
    current_password: '',
    new_password: '',
    confirm_password: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push(ROUTES.LOGIN);
    }
  }, [isAuthenticated, authLoading, router]);

  // Load user data
  useEffect(() => {
    if (user) {
      setAccountForm({
        name: user.name,
        email: user.email,
        language: user.language as 'en' | 'de' | 'ar',
      });
    }
  }, [user]);

  // Load user profile
  useEffect(() => {
    if (isAuthenticated && activeTab === 'profile') {
      fetchUserProfile()
        .then(setUserProfile)
        .catch((error) => {
          toast.error('Failed to load profile', error.message);
        });
    }
  }, [isAuthenticated, activeTab, fetchUserProfile, toast]);

  const languageOptions = Object.entries(SUPPORTED_LANGUAGES).map(([code, name]) => ({
    value: code,
    label: name,
    icon: <Globe className="h-4 w-4" />,
  }));

  const validateAccountForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!accountForm.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!accountForm.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!validateEmail(accountForm.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateSecurityForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!securityForm.current_password) {
      newErrors.current_password = 'Current password is required';
    }

    if (!securityForm.new_password) {
      newErrors.new_password = 'New password is required';
    } else {
      const passwordValidation = validatePassword(securityForm.new_password);
      if (!passwordValidation.isValid) {
        newErrors.new_password = passwordValidation.errors[0];
      }
    }

    if (!securityForm.confirm_password) {
      newErrors.confirm_password = 'Please confirm your new password';
    } else if (securityForm.new_password !== securityForm.confirm_password) {
      newErrors.confirm_password = 'Passwords do not match';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleAccountSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateAccountForm()) return;

    setIsLoading(true);
    try {
      const updateData: UserUpdatePayload = {
        name: accountForm.name,
        email: accountForm.email,
        language: accountForm.language,
      };

      await updateAccount(updateData);
      toast.success('Account updated successfully');
    } catch (error: any) {
      toast.error('Failed to update account', error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSecuritySubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateSecurityForm()) return;

    setIsLoading(true);
    try {
      const updateData: UserUpdatePayload = {
        current_password: securityForm.current_password,
        new_password: securityForm.new_password,
        confirm_password: securityForm.confirm_password,
      };

      await updateAccount(updateData);
      toast.success('Password updated successfully');
      
      // Clear form
      setSecurityForm({
        current_password: '',
        new_password: '',
        confirm_password: '',
      });
    } catch (error: any) {
      toast.error('Failed to update password', error.message);
    } finally {
      setIsLoading(false);
    }
  };

  if (authLoading) {
    return (
      <MainLayout>
        <PageLoading text="Loading your profile..." />
      </MainLayout>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Profile Settings</h1>
          <p className="text-muted mt-1">
            Manage your account settings and preferences
          </p>
        </div>

        {/* Tabs */}
        <div className="border-b border-border mb-8">
          <nav className="flex space-x-8">
            {[
              { id: 'account', label: 'Account Details', icon: User },
              { id: 'security', label: 'Security', icon: Shield },
              { id: 'profile', label: 'Profile Info', icon: Globe },
            ].map(({ id, label, icon: Icon }) => (
              <button
                key={id}
                onClick={() => setActiveTab(id as any)}
                className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted hover:text-foreground'
                }`}
              >
                <Icon className="h-4 w-4" />
                {label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="space-y-6">
          {activeTab === 'account' && (
            <form onSubmit={handleAccountSubmit} className="space-y-6">
              <div className="bg-card border border-border rounded-lg p-6">
                <h2 className="text-xl font-semibold mb-4">Account Information</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Input
                    label="Full Name"
                    value={accountForm.name}
                    onChange={(e) => {
                      setAccountForm(prev => ({ ...prev, name: e.target.value }));
                      if (errors.name) setErrors(prev => ({ ...prev, name: '' }));
                    }}
                    error={errors.name}
                    required
                  />

                  <Input
                    label="Email Address"
                    type="email"
                    value={accountForm.email}
                    onChange={(e) => {
                      setAccountForm(prev => ({ ...prev, email: e.target.value }));
                      if (errors.email) setErrors(prev => ({ ...prev, email: '' }));
                    }}
                    error={errors.email}
                    required
                  />

                  <Dropdown
                    label="Preferred Language"
                    options={languageOptions}
                    value={accountForm.language}
                    onChange={(value) => {
                      setAccountForm(prev => ({ ...prev, language: value as 'en' | 'de' | 'ar' }));
                    }}
                  />
                </div>

                <div className="flex justify-end mt-6">
                  <Button type="submit" isLoading={isLoading}>
                    Save Changes
                  </Button>
                </div>
              </div>
            </form>
          )}

          {activeTab === 'security' && (
            <form onSubmit={handleSecuritySubmit} className="space-y-6">
              <div className="bg-card border border-border rounded-lg p-6">
                <h2 className="text-xl font-semibold mb-4">Change Password</h2>
                
                <div className="space-y-4 max-w-md">
                  <div className="relative">
                    <Input
                      label="Current Password"
                      type={showCurrentPassword ? 'text' : 'password'}
                      value={securityForm.current_password}
                      onChange={(e) => {
                        setSecurityForm(prev => ({ ...prev, current_password: e.target.value }));
                        if (errors.current_password) setErrors(prev => ({ ...prev, current_password: '' }));
                      }}
                      error={errors.current_password}
                      required
                    />
                    <button
                      type="button"
                      className="absolute right-3 top-9 text-muted hover:text-foreground"
                      onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                    >
                      {showCurrentPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                  </div>

                  <div className="relative">
                    <Input
                      label="New Password"
                      type={showNewPassword ? 'text' : 'password'}
                      value={securityForm.new_password}
                      onChange={(e) => {
                        setSecurityForm(prev => ({ ...prev, new_password: e.target.value }));
                        if (errors.new_password) setErrors(prev => ({ ...prev, new_password: '' }));
                      }}
                      error={errors.new_password}
                      required
                    />
                    <button
                      type="button"
                      className="absolute right-3 top-9 text-muted hover:text-foreground"
                      onClick={() => setShowNewPassword(!showNewPassword)}
                    >
                      {showNewPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                  </div>

                  <div className="relative">
                    <Input
                      label="Confirm New Password"
                      type={showConfirmPassword ? 'text' : 'password'}
                      value={securityForm.confirm_password}
                      onChange={(e) => {
                        setSecurityForm(prev => ({ ...prev, confirm_password: e.target.value }));
                        if (errors.confirm_password) setErrors(prev => ({ ...prev, confirm_password: '' }));
                      }}
                      error={errors.confirm_password}
                      required
                    />
                    <button
                      type="button"
                      className="absolute right-3 top-9 text-muted hover:text-foreground"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    >
                      {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                  </div>
                </div>

                <div className="flex justify-end mt-6">
                  <Button type="submit" isLoading={isLoading}>
                    Update Password
                  </Button>
                </div>
              </div>
            </form>
          )}

          {activeTab === 'profile' && (
            <div className="bg-card border border-border rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Profile Information</h2>
              
              {userProfile ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="text-sm font-medium text-muted">User ID</label>
                    <p className="mt-1 font-mono text-sm">{userProfile.id}</p>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-muted">Email Verified</label>
                    <p className="mt-1">
                      {userProfile.email_verified ? (
                        <span className="text-green-600">✓ Verified</span>
                      ) : (
                        <span className="text-yellow-600">⚠ Not verified</span>
                      )}
                    </p>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-muted">Account Created</label>
                    <p className="mt-1 flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      {formatDateTime(userProfile.created_at)}
                    </p>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-muted">Last Updated</label>
                    <p className="mt-1 flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      {formatDateTime(userProfile.updated_at)}
                    </p>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-muted">Last Login</label>
                    <p className="mt-1">
                      {userProfile.last_login_at 
                        ? formatDateTime(userProfile.last_login_at)
                        : 'Never'
                      }
                    </p>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-muted">Login Attempts</label>
                    <p className="mt-1">{userProfile.login_attempts}</p>
                  </div>

                  {userProfile.locked_until && (
                    <div className="md:col-span-2">
                      <label className="text-sm font-medium text-red-600">Account Locked Until</label>
                      <p className="mt-1 text-red-600">
                        {formatDateTime(userProfile.locked_until)}
                      </p>
                    </div>
                  )}
                </div>
              ) : (
                <PageLoading text="Loading profile information..." />
              )}
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
}
