'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Eye, EyeOff, Globe } from 'lucide-react';
import { MainLayout } from '@/components/layout/MainLayout';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Dropdown } from '@/components/ui/Dropdown';
import { useAuthStore } from '@/stores/auth';
import { useToast } from '@/components/ui/ToastNotification';
import { ROUTES, SUPPORTED_LANGUAGES } from '@/lib/constants';
import { validateEmail, validatePassword } from '@/lib/utils';
import type { RegisterPayload } from '@/types/auth';

export default function RegisterPage() {
  const router = useRouter();
  const { register, isLoading, error, clearError } = useAuthStore();
  const { toast } = useToast();
  
  const [formData, setFormData] = useState<RegisterPayload>({
    name: '',
    email: '',
    language: 'en',
    password: '',
    confirm_password: '',
  });
  
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [errors, setErrors] = useState<Partial<RegisterPayload>>({});

  const languageOptions = Object.entries(SUPPORTED_LANGUAGES).map(([code, name]) => ({
    value: code,
    label: name,
    icon: <Globe className="h-4 w-4" />,
  }));

  const validateForm = (): boolean => {
    const newErrors: Partial<RegisterPayload> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!validateEmail(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else {
      const passwordValidation = validatePassword(formData.password);
      if (!passwordValidation.isValid) {
        newErrors.password = passwordValidation.errors[0];
      }
    }

    if (!formData.confirm_password) {
      newErrors.confirm_password = 'Please confirm your password';
    } else if (formData.password !== formData.confirm_password) {
      newErrors.confirm_password = 'Passwords do not match';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError();

    if (!validateForm()) {
      return;
    }

    try {
      await register(formData);
      toast.success('Account created successfully!', 'Welcome to CV Maker');
      router.push(ROUTES.DASHBOARD);
    } catch (error: any) {
      toast.error('Registration failed', error.message);
    }
  };

  const handleInputChange = (field: keyof RegisterPayload, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <MainLayout showFooter={false}>
      <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold">Create Your Account</h2>
            <p className="mt-2 text-muted">
              Join thousands of professionals building their careers
            </p>
          </div>

          <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
            <div className="space-y-4">
              <Input
                label="Full Name"
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                error={errors.name}
                placeholder="Enter your full name"
                required
              />

              <Input
                label="Email Address"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                error={errors.email}
                placeholder="Enter your email address"
                required
              />

              <Dropdown
                label="Preferred Language"
                options={languageOptions}
                value={formData.language}
                onChange={(value) => handleInputChange('language', value as 'en' | 'de' | 'ar')}
                placeholder="Select your preferred language"
              />

              <div className="relative">
                <Input
                  label="Password"
                  type={showPassword ? 'text' : 'password'}
                  value={formData.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  error={errors.password}
                  placeholder="Create a strong password"
                  required
                />
                <button
                  type="button"
                  className="absolute right-3 top-9 text-muted hover:text-foreground"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>

              <div className="relative">
                <Input
                  label="Confirm Password"
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={formData.confirm_password}
                  onChange={(e) => handleInputChange('confirm_password', e.target.value)}
                  error={errors.confirm_password}
                  placeholder="Confirm your password"
                  required
                />
                <button
                  type="button"
                  className="absolute right-3 top-9 text-muted hover:text-foreground"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
            </div>

            {error && (
              <div className="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-md text-sm">
                {error}
              </div>
            )}

            <Button
              type="submit"
              className="w-full"
              size="lg"
              isLoading={isLoading}
            >
              Create Account
            </Button>

            <div className="text-center">
              <p className="text-sm text-muted">
                Already have an account?{' '}
                <Link
                  href={ROUTES.LOGIN}
                  className="font-medium text-primary hover:text-primary/80"
                >
                  Sign in here
                </Link>
              </p>
            </div>

            <div className="text-center text-xs text-muted">
              By creating an account, you agree to our{' '}
              <Link href={ROUTES.TERMS} className="text-primary hover:text-primary/80">
                Terms & Conditions
              </Link>{' '}
              and{' '}
              <Link href={ROUTES.PRIVACY} className="text-primary hover:text-primary/80">
                Privacy Policy
              </Link>
            </div>
          </form>
        </div>
      </div>
    </MainLayout>
  );
}
