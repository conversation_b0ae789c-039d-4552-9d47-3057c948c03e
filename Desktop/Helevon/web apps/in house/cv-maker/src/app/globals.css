
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";

:root {
  /* Light theme (default) */
  --background: #ffffff;
  --foreground: #333333;
  --primary: #00796B;
  --primary-foreground: #ffffff;
  --secondary: #F5F5F5;
  --secondary-foreground: #333333;
  --accent: #FF9800;
  --accent-foreground: #ffffff;
  --muted: #757575;
  --border: #E0E0E0;
  --input: #ffffff;
  --ring: #00796B;
  --card: #ffffff;
  --card-foreground: #333333;
  --popover: #ffffff;
  --popover-foreground: #333333;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
}

/* Dark theme - only when explicitly requested */
[data-theme="dark"] {
  --background: #0a0a0a;
  --foreground: #ededed;
  --primary: #4DB6AC;
  --primary-foreground: #0a0a0a;
  --secondary: #1E1E1E;
  --secondary-foreground: #ededed;
  --accent: #FFB74D;
  --accent-foreground: #0a0a0a;
  --muted: #A0A0A0;
  --border: #2E2E2E;
  --input: #1E1E1E;
  --ring: #4DB6AC;
  --card: #1E1E1E;
  --card-foreground: #ededed;
  --popover: #1E1E1E;
  --popover-foreground: #ededed;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
}

/* Optional: Auto dark mode based on system preference (commented out to force light theme) */
/*
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --primary: #4DB6AC;
    --primary-foreground: #0a0a0a;
    --secondary: #1E1E1E;
    --secondary-foreground: #ededed;
    --accent: #FFB74D;
    --accent-foreground: #0a0a0a;
    --muted: #A0A0A0;
    --border: #2E2E2E;
    --input: #1E1E1E;
    --ring: #4DB6AC;
    --card: #1E1E1E;
    --card-foreground: #ededed;
    --popover: #1E1E1E;
    --popover-foreground: #ededed;
  }
}
*/

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  font-feature-settings: 'cv11', 'ss01';
  font-variation-settings: 'opsz' 32;
}

/* Smooth transitions for better UX */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Focus styles for accessibility */
*:focus-visible {
  outline: 2px solid var(--ring);
  outline-offset: 2px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--muted);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary);
}
