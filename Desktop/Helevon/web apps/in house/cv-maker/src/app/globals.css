
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";

:root {
  /* Premium Light Theme - Rich & Sophisticated */
  --background: #f8fafc;
  --background-elevated: #ffffff;
  --background-subtle: #f1f5f9;
  --background-muted: #e2e8f0;
  --foreground: #0f172a;
  --foreground-muted: #475569;
  --foreground-subtle: #64748b;

  /* Primary Gradient - Sophisticated Ocean Blue */
  --primary: #0ea5e9;
  --primary-dark: #0284c7;
  --primary-light: #38bdf8;
  --primary-foreground: #ffffff;
  --primary-gradient: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 50%, #6366f1 100%);
  --primary-glow: 0 0 60px rgba(14, 165, 233, 0.4);
  --primary-shadow: 0 10px 40px rgba(14, 165, 233, 0.2);

  /* Secondary - Rich Neutrals with Warmth */
  --secondary: #f1f5f9;
  --secondary-dark: #e2e8f0;
  --secondary-light: #f8fafc;
  --secondary-foreground: #475569;
  --secondary-gradient: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);

  /* Accent - Vibrant Purple with Depth */
  --accent: #8b5cf6;
  --accent-dark: #7c3aed;
  --accent-light: #a78bfa;
  --accent-foreground: #ffffff;
  --accent-gradient: linear-gradient(135deg, #8b5cf6 0%, #a855f7 50%, #c084fc 100%);
  --accent-glow: 0 0 60px rgba(139, 92, 246, 0.4);
  --accent-shadow: 0 10px 40px rgba(139, 92, 246, 0.2);

  /* Success - Rich Emerald */
  --success: #10b981;
  --success-light: #34d399;
  --success-dark: #059669;
  --success-foreground: #ffffff;
  --success-gradient: linear-gradient(135deg, #10b981 0%, #34d399 100%);
  --success-glow: 0 0 40px rgba(16, 185, 129, 0.3);

  /* Warning - Rich Amber */
  --warning: #f59e0b;
  --warning-light: #fbbf24;
  --warning-dark: #d97706;
  --warning-foreground: #ffffff;
  --warning-gradient: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
  --warning-glow: 0 0 40px rgba(245, 158, 11, 0.3);

  /* Destructive - Rich Rose */
  --destructive: #ef4444;
  --destructive-light: #f87171;
  --destructive-dark: #dc2626;
  --destructive-foreground: #ffffff;
  --destructive-gradient: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
  --destructive-glow: 0 0 40px rgba(239, 68, 68, 0.3);

  /* Borders & Surfaces with Depth */
  --border: #e2e8f0;
  --border-light: #f1f5f9;
  --border-strong: #cbd5e1;
  --input: #ffffff;
  --ring: var(--primary);
  --card: #ffffff;
  --card-foreground: var(--foreground);
  --popover: #ffffff;
  --popover-foreground: var(--foreground);

  /* Enhanced Glass Effects */
  --glass: rgba(255, 255, 255, 0.85);
  --glass-border: rgba(255, 255, 255, 0.3);
  --glass-shadow: 0 8px 32px rgba(15, 23, 42, 0.12);
  --glass-backdrop: blur(16px);

  /* Sophisticated Shadows with Depth */
  --shadow-sm: 0 1px 2px 0 rgba(15, 23, 42, 0.05);
  --shadow: 0 4px 6px -1px rgba(15, 23, 42, 0.08), 0 2px 4px -1px rgba(15, 23, 42, 0.04);
  --shadow-md: 0 10px 15px -3px rgba(15, 23, 42, 0.1), 0 4px 6px -2px rgba(15, 23, 42, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(15, 23, 42, 0.12), 0 10px 10px -5px rgba(15, 23, 42, 0.06);
  --shadow-xl: 0 25px 50px -12px rgba(15, 23, 42, 0.15);
  --shadow-2xl: 0 35px 60px -12px rgba(15, 23, 42, 0.2);
  --shadow-glow: 0 0 0 1px rgba(14, 165, 233, 0.1), 0 8px 32px rgba(14, 165, 233, 0.15);
  --shadow-colored: 0 10px 40px rgba(14, 165, 233, 0.15);
  --shadow-accent: 0 10px 40px rgba(139, 92, 246, 0.15);

  /* Background Textures & Patterns */
  --bg-pattern-dots: radial-gradient(circle at 1px 1px, rgba(15, 23, 42, 0.04) 1px, transparent 0);
  --bg-pattern-grid: linear-gradient(rgba(15, 23, 42, 0.02) 1px, transparent 1px), linear-gradient(90deg, rgba(15, 23, 42, 0.02) 1px, transparent 1px);
  --bg-gradient-subtle: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%);
  --bg-gradient-warm: linear-gradient(135deg, #fef7f0 0%, #fef3ec 50%, #fed7aa 100%);
  --bg-gradient-cool: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 50%, #bae6fd 100%);

  /* Enhanced Radius */
  --radius: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-3xl: 2rem;
}

/* Premium Dark Theme - Sophisticated & Dramatic */
[data-theme="dark"] {
  --background: #0f0f23;
  --background-elevated: #1a1a2e;
  --foreground: #e2e8f0;
  --foreground-muted: #94a3b8;

  /* Primary - Bright Cyan */
  --primary: #06b6d4;
  --primary-dark: #0891b2;
  --primary-light: #22d3ee;
  --primary-foreground: #0f0f23;
  --primary-gradient: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
  --primary-glow: 0 0 40px rgba(6, 182, 212, 0.4);

  /* Secondary - Cool Grays */
  --secondary: #1e293b;
  --secondary-dark: #334155;
  --secondary-foreground: #cbd5e1;

  /* Accent - Electric Purple */
  --accent: #a855f7;
  --accent-dark: #9333ea;
  --accent-light: #c084fc;
  --accent-foreground: #ffffff;
  --accent-gradient: linear-gradient(135deg, #a855f7 0%, #ec4899 100%);
  --accent-glow: 0 0 40px rgba(168, 85, 247, 0.4);

  /* Success - Emerald */
  --success: #10b981;
  --success-light: #34d399;
  --success-foreground: #ffffff;

  /* Warning - Amber */
  --warning: #f59e0b;
  --warning-light: #fbbf24;
  --warning-foreground: #0f0f23;

  /* Destructive - Rose */
  --destructive: #ef4444;
  --destructive-light: #f87171;
  --destructive-foreground: #ffffff;

  /* Borders & Surfaces */
  --border: #334155;
  --border-light: #475569;
  --input: #1e293b;
  --ring: var(--primary);
  --card: #1a1a2e;
  --card-foreground: var(--foreground);
  --popover: #1a1a2e;
  --popover-foreground: var(--foreground);

  /* Glass Effect */
  --glass: rgba(26, 26, 46, 0.8);
  --glass-border: rgba(255, 255, 255, 0.1);

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.6);
  --shadow-glow: 0 0 0 1px rgba(6, 182, 212, 0.2), 0 8px 32px rgba(6, 182, 212, 0.3);
}

/* Optional: Auto dark mode based on system preference (commented out to force light theme) */
/*
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --primary: #4DB6AC;
    --primary-foreground: #0a0a0a;
    --secondary: #1E1E1E;
    --secondary-foreground: #ededed;
    --accent: #FFB74D;
    --accent-foreground: #0a0a0a;
    --muted: #A0A0A0;
    --border: #2E2E2E;
    --input: #1E1E1E;
    --ring: #4DB6AC;
    --card: #1E1E1E;
    --card-foreground: #ededed;
    --popover: #1E1E1E;
    --popover-foreground: #ededed;
  }
}
*/

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  font-feature-settings: 'cv11', 'ss01';
  font-variation-settings: 'opsz' 32;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* Enhanced transitions for fluid UX */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, transform, box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* Focus styles with glow effect */
*:focus-visible {
  outline: none;
  box-shadow: var(--shadow-glow);
}

/* Premium scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, var(--primary), var(--primary-dark));
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, var(--primary-light), var(--primary));
}

/* Glass morphism utility */
.glass {
  background: var(--glass);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
}

/* Gradient text utility */
.gradient-text {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glow effects */
.glow-primary {
  box-shadow: var(--primary-glow);
}

.glow-accent {
  box-shadow: var(--accent-glow);
}

/* Animation utilities */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 20px rgba(14, 165, 233, 0.3); }
  50% { box-shadow: 0 0 40px rgba(14, 165, 233, 0.6); }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

/* Enhanced Background Patterns for Rich Light Theme */
.bg-pattern-dots {
  background-image: var(--bg-pattern-dots);
  background-size: 20px 20px;
}

.bg-pattern-grid {
  background-image: var(--bg-pattern-grid);
  background-size: 20px 20px;
}

.bg-gradient-subtle {
  background: var(--bg-gradient-subtle);
}

.bg-gradient-warm {
  background: var(--bg-gradient-warm);
}

.bg-gradient-cool {
  background: var(--bg-gradient-cool);
}

/* Enhanced Glass Effects */
.glass {
  background: var(--glass);
  border: 1px solid var(--glass-border);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  box-shadow: var(--glass-shadow);
}

/* Rich Card Styles */
.card-elevated {
  background: var(--background-elevated);
  border: 1px solid var(--border);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-elevated:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
  border-color: var(--border-strong);
}

/* Enhanced Shadow Utilities */
.shadow-colored {
  box-shadow: var(--shadow-colored);
}

.shadow-accent {
  box-shadow: var(--shadow-accent);
}

.shadow-glow {
  box-shadow: var(--shadow-glow);
}

/* Selection styling */
::selection {
  background: var(--primary);
  color: var(--primary-foreground);
}

::-moz-selection {
  background: var(--primary);
  color: var(--primary-foreground);
}
