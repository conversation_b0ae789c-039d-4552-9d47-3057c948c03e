import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "CV Maker - Create Professional CVs with Ease",
  description: "Build stunning, professional CVs with our intuitive CV maker. Choose from multiple templates, support for multiple languages, and export to PDF.",
  keywords: "CV maker, resume builder, professional CV, job application, career",
  authors: [{ name: "CV Maker Team" }],
  openGraph: {
    title: "CV Maker - Create Professional CVs with Ease",
    description: "Build stunning, professional CVs with our intuitive CV maker.",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.variable} font-sans antialiased`}>
        {children}
      </body>
    </html>
  );
}
