'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  FileText, 
  Sparkles, 
  Globe, 
  Camera, 
  Star, 
  Filter,
  ArrowRight,
  Check,
  Eye,
  Palette,
  Briefcase,
  GraduationCap,
  Code,
  Heart
} from 'lucide-react';
import { MainLayout } from '@/components/layout/MainLayout';
import { Button } from '@/components/ui/Button';
import { Dropdown } from '@/components/ui/Dropdown';
import { 
  FadeInUp, 
  FadeInScale, 
  StaggerContainer,
  MagneticElement,
  FloatingElement
} from '@/components/motion/MotionComponents';
import { useAuthStore } from '@/stores/auth';
import { useTemplateStore } from '@/stores/templates';
import { useToast } from '@/components/ui/ToastNotification';
import { ROUTES, SUPPORTED_LANGUAGES, CV_TEMPLATES } from '@/lib/constants';
import type { CVTemplate } from '@/types/templates';

export default function TemplatesPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const { templates, isLoading, error, fetchTemplates } = useTemplateStore();
  const { toast } = useToast();
  
  const [selectedLanguage, setSelectedLanguage] = useState<'en' | 'de' | 'ar'>(user?.language || 'en');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      router.push(ROUTES.LOGIN);
    }
  }, [isAuthenticated, router]);

  // Fetch templates on mount
  useEffect(() => {
    fetchTemplates().catch((error) => {
      console.error('Failed to load templates:', error);
    });
  }, []);

  const languageOptions = Object.entries(SUPPORTED_LANGUAGES).map(([code, name]) => ({
    value: code,
    label: name,
    icon: <Globe className="h-4 w-4" />,
  }));

  const categoryOptions = [
    { value: 'all', label: 'All Templates', icon: <FileText className="h-4 w-4" /> },
    { value: 'professional', label: 'Professional', icon: <Briefcase className="h-4 w-4" /> },
    { value: 'academic', label: 'Academic', icon: <GraduationCap className="h-4 w-4" /> },
    { value: 'creative', label: 'Creative', icon: <Palette className="h-4 w-4" /> },
    { value: 'technical', label: 'Technical', icon: <Code className="h-4 w-4" /> },
  ];

  // Mock templates data (replace with actual data from store)
  const mockTemplates: CVTemplate[] = [
    {
      id: 'modern-professional',
      name: 'Modern Professional',
      description: 'Clean and contemporary design perfect for business professionals',
      category: 'professional',
      languages: ['en', 'de', 'ar'],
      features: ['ATS-Friendly', 'Photo Optional', 'Skills Section'],
      preview_url: '/templates/modern-professional/preview.jpg',
      is_premium: false,
      cultural_notes: {
        en: 'International standard format',
        de: 'Adaptable for German market with photo section',
        ar: 'RTL layout compatible'
      }
    },
    {
      id: 'german-standard',
      name: 'German Standard',
      description: 'Traditional German CV format with photo and personal details',
      category: 'professional',
      languages: ['de', 'en'],
      features: ['Photo Required', 'Personal Details', 'Chronological'],
      preview_url: '/templates/german-standard/preview.jpg',
      is_premium: false,
      cultural_notes: {
        de: 'Standard German Lebenslauf format',
        en: 'German-style format for European applications'
      }
    },
    {
      id: 'arabic-elegant',
      name: 'Arabic Elegant',
      description: 'Beautiful RTL design optimized for Arabic content',
      category: 'professional',
      languages: ['ar', 'en'],
      features: ['RTL Layout', 'Arabic Typography', 'Cultural Sections'],
      preview_url: '/templates/arabic-elegant/preview.jpg',
      is_premium: true,
      cultural_notes: {
        ar: 'تصميم أنيق للسيرة الذاتية العربية',
        en: 'Arabic-optimized design with RTL support'
      }
    },
    {
      id: 'creative-portfolio',
      name: 'Creative Portfolio',
      description: 'Vibrant design for creative professionals and artists',
      category: 'creative',
      languages: ['en', 'de'],
      features: ['Portfolio Section', 'Color Customization', 'Visual Focus'],
      preview_url: '/templates/creative-portfolio/preview.jpg',
      is_premium: true,
      cultural_notes: {
        en: 'Perfect for creative industries',
        de: 'Ideal for creative professionals in German market'
      }
    },
    {
      id: 'academic-research',
      name: 'Academic Research',
      description: 'Comprehensive format for academic and research positions',
      category: 'academic',
      languages: ['en', 'de'],
      features: ['Publications Section', 'Research Focus', 'Detailed Format'],
      preview_url: '/templates/academic-research/preview.jpg',
      is_premium: false,
      cultural_notes: {
        en: 'Standard academic CV format',
        de: 'German academic standards compliant'
      }
    },
    {
      id: 'tech-minimal',
      name: 'Tech Minimal',
      description: 'Clean, minimal design for tech professionals',
      category: 'technical',
      languages: ['en', 'de'],
      features: ['Skills Matrix', 'Project Showcase', 'GitHub Integration'],
      preview_url: '/templates/tech-minimal/preview.jpg',
      is_premium: false,
      cultural_notes: {
        en: 'Perfect for software developers',
        de: 'Tech-focused format for German IT market'
      }
    }
  ];

  // Filter templates based on selected criteria
  const filteredTemplates = mockTemplates.filter(template => {
    const languageMatch = template.languages.includes(selectedLanguage);
    const categoryMatch = selectedCategory === 'all' || template.category === selectedCategory;
    return languageMatch && categoryMatch;
  });

  const handleTemplateSelect = (templateId: string) => {
    setSelectedTemplate(templateId);
  };

  const handleContinue = () => {
    if (!selectedTemplate) {
      toast.error('Please select a template', 'Choose a template to continue');
      return;
    }

    // Navigate to CV creation with selected template
    router.push(`${ROUTES.CV_CREATE}?template=${selectedTemplate}&language=${selectedLanguage}`);
  };

  if (!isAuthenticated) {
    return null; // Will redirect
  }

  return (
    <MainLayout>
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-cool py-20">
        {/* Enhanced Background with Rich Patterns */}
        <div className="absolute inset-0 bg-pattern-grid opacity-15" />
        <div className="absolute inset-0 bg-gradient-to-br from-primary/4 via-transparent to-accent/4" />

        {/* Floating Background Elements */}
        <FloatingElement className="absolute top-10 left-10 opacity-25" intensity={15} duration={6}>
          <div className="w-32 h-32 rounded-full bg-gradient-to-r from-primary to-accent blur-xl" />
        </FloatingElement>
        <FloatingElement className="absolute bottom-10 right-10 opacity-25" intensity={20} duration={8}>
          <div className="w-40 h-40 rounded-full bg-gradient-to-r from-accent to-success blur-2xl" />
        </FloatingElement>

        <div className="container mx-auto px-4 relative z-10">
          <StaggerContainer>
            <FadeInUp className="text-center mb-12">
              <motion.div
                className="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-gradient-to-r from-primary/10 to-accent/10 border border-primary/20 mb-6"
                whileHover={{ scale: 1.05 }}
              >
                <Sparkles className="h-5 w-5 text-primary" />
                <span className="font-medium text-primary">Choose Your Perfect Template</span>
              </motion.div>
              
              <h1 className="text-4xl lg:text-6xl font-bold tracking-tight mb-4">
                Professional{' '}
                <span className="gradient-text bg-gradient-to-r from-primary via-accent to-success bg-[length:200%_200%] animate-gradient">
                  CV Templates
                </span>
              </h1>
              
              <p className="text-xl text-foreground-muted max-w-3xl mx-auto leading-relaxed">
                Choose from our collection of professionally designed templates, 
                optimized for different cultures and industries.
              </p>
            </FadeInUp>

            {/* Filters */}
            <FadeInUp delay={0.2} className="max-w-4xl mx-auto mb-16">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium mb-2">CV Language</label>
                  <Dropdown
                    options={languageOptions}
                    value={selectedLanguage}
                    onChange={(value) => setSelectedLanguage(value as 'en' | 'de' | 'ar')}
                    className="w-full"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">Category</label>
                  <Dropdown
                    options={categoryOptions}
                    value={selectedCategory}
                    onChange={setSelectedCategory}
                    className="w-full"
                  />
                </div>
              </div>
            </FadeInUp>
          </StaggerContainer>
        </div>
      </section>

      {/* Templates Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <StaggerContainer>
            {/* Results Header */}
            <FadeInUp className="mb-12">
              <div className="flex justify-between items-center">
                <h2 className="text-2xl font-bold">
                  {filteredTemplates.length} Template{filteredTemplates.length !== 1 ? 's' : ''} Found
                </h2>
                <div className="text-sm text-foreground-muted">
                  Showing templates for {SUPPORTED_LANGUAGES[selectedLanguage]}
                </div>
              </div>
            </FadeInUp>

            {/* Templates Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredTemplates.map((template, index) => (
                <FadeInUp key={template.id} delay={index * 0.1}>
                  <MagneticElement>
                    <motion.div
                      className={`relative group cursor-pointer bg-background-elevated rounded-3xl overflow-hidden border-2 transition-all duration-300 ${
                        selectedTemplate === template.id 
                          ? 'border-primary shadow-lg ring-4 ring-primary/20' 
                          : 'border-border hover:border-primary/50'
                      }`}
                      whileHover={{ y: -8, scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => handleTemplateSelect(template.id)}
                    >
                      {/* Template Preview */}
                      <div className="aspect-[3/4] bg-gradient-to-br from-secondary/30 to-accent/10 relative overflow-hidden">
                        {/* Mock preview content */}
                        <div className="absolute inset-4 bg-white rounded-lg shadow-lg p-4 space-y-2">
                          <div className="h-3 bg-primary rounded w-3/4"></div>
                          <div className="h-2 bg-gray-200 rounded w-1/2"></div>
                          <div className="space-y-1 mt-4">
                            <div className="h-1.5 bg-gray-100 rounded"></div>
                            <div className="h-1.5 bg-gray-100 rounded w-5/6"></div>
                            <div className="h-1.5 bg-gray-100 rounded w-4/6"></div>
                          </div>
                          <div className="pt-2 space-y-1">
                            <div className="h-2 bg-accent rounded w-2/3"></div>
                            <div className="h-1.5 bg-gray-100 rounded"></div>
                            <div className="h-1.5 bg-gray-100 rounded w-3/4"></div>
                          </div>
                        </div>

                        {/* Premium Badge */}
                        {template.is_premium && (
                          <div className="absolute top-4 right-4 bg-gradient-to-r from-accent to-accent-dark text-white px-3 py-1 rounded-full text-xs font-bold">
                            Premium
                          </div>
                        )}

                        {/* Selection Indicator */}
                        {selectedTemplate === template.id && (
                          <motion.div
                            className="absolute inset-0 bg-primary/10 flex items-center justify-center"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                          >
                            <div className="bg-primary text-white rounded-full p-3">
                              <Check className="h-6 w-6" />
                            </div>
                          </motion.div>
                        )}

                        {/* Hover Overlay */}
                        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                          <Button variant="glass" size="sm">
                            <Eye className="h-4 w-4 mr-2" />
                            Preview
                          </Button>
                        </div>
                      </div>

                      {/* Template Info */}
                      <div className="p-6">
                        <div className="flex items-start justify-between mb-3">
                          <h3 className="text-lg font-bold group-hover:text-primary transition-colors">
                            {template.name}
                          </h3>
                          <div className="flex items-center gap-1">
                            {template.languages.map(lang => (
                              <span
                                key={lang}
                                className={`text-xs px-2 py-1 rounded-full ${
                                  lang === selectedLanguage 
                                    ? 'bg-primary text-white' 
                                    : 'bg-secondary text-foreground-muted'
                                }`}
                              >
                                {lang.toUpperCase()}
                              </span>
                            ))}
                          </div>
                        </div>

                        <p className="text-foreground-muted text-sm mb-4 leading-relaxed">
                          {template.description}
                        </p>

                        {/* Features */}
                        <div className="flex flex-wrap gap-2 mb-4">
                          {template.features.slice(0, 3).map(feature => (
                            <span
                              key={feature}
                              className="text-xs px-2 py-1 bg-accent/10 text-accent rounded-full"
                            >
                              {feature}
                            </span>
                          ))}
                        </div>

                        {/* Cultural Notes */}
                        {template.cultural_notes[selectedLanguage] && (
                          <div className="text-xs text-foreground-muted bg-secondary/30 rounded-lg p-3">
                            <Globe className="h-3 w-3 inline mr-1" />
                            {template.cultural_notes[selectedLanguage]}
                          </div>
                        )}
                      </div>
                    </motion.div>
                  </MagneticElement>
                </FadeInUp>
              ))}
            </div>

            {/* Continue Button */}
            {selectedTemplate && (
              <FadeInScale className="text-center mt-16">
                <MagneticElement>
                  <Button
                    onClick={handleContinue}
                    size="xl"
                    gradient
                    glow
                    className="group"
                  >
                    Continue with Selected Template
                    <ArrowRight className="h-6 w-6 ml-2 group-hover:translate-x-2 transition-transform" />
                  </Button>
                </MagneticElement>
              </FadeInScale>
            )}
          </StaggerContainer>
        </div>
      </section>
    </MainLayout>
  );
}
