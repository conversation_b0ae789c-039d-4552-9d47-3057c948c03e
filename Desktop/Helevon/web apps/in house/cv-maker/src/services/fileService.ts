import { apiClient } from './api';

export interface FileUploadOptions {
  cvId: string;
  file: File;
  category: 'profile_photo' | 'certificate' | 'signature' | 'other';
}

export interface FileValidationResult {
  isValid: boolean;
  error?: string;
}

export class FileService {
  // File validation based on FileManagement.md specifications
  static validateFile(file: File, category: string): FileValidationResult {
    const maxSizes = {
      profile_photo: 5 * 1024 * 1024, // 5MB
      certificate: 10 * 1024 * 1024, // 10MB
      signature: 2 * 1024 * 1024, // 2MB
      other: 10 * 1024 * 1024 // 10MB
    };

    const allowedTypes = {
      profile_photo: ['image/jpeg', 'image/png', 'image/jpg'],
      certificate: ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg'],
      signature: ['image/jpeg', 'image/png', 'image/jpg'],
      other: ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg', 'text/plain']
    };

    const maxSize = maxSizes[category as keyof typeof maxSizes] || 10 * 1024 * 1024;
    const types = allowedTypes[category as keyof typeof allowedTypes] || [];

    if (file.size > maxSize) {
      return {
        isValid: false,
        error: `File size exceeds ${(maxSize / 1024 / 1024).toFixed(0)}MB limit`
      };
    }

    if (!types.includes(file.type)) {
      return {
        isValid: false,
        error: `File type ${file.type} is not allowed for ${category}`
      };
    }

    return { isValid: true };
  }

  // Upload file using the API endpoint from FileManagement.md
  static async uploadFile({ cvId, file, category }: FileUploadOptions): Promise<any> {
    // Validate file first
    const validation = this.validateFile(file, category);
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('category', category);

      // Use the exact endpoint from FileManagement.md
      const response = await apiClient.postFormData(`/cvs/${cvId}/files`, formData);
      return response;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to upload file');
    }
  }

  // Delete file using the API endpoint from FileManagement.md
  static async deleteFile(fileId: string): Promise<void> {
    try {
      // Use the exact endpoint from FileManagement.md
      await apiClient.delete(`/files/${fileId}`);
    } catch (error: any) {
      throw new Error(error.message || 'Failed to delete file');
    }
  }

  // Get file list for a CV
  static async getFileList(cvId: string): Promise<any[]> {
    try {
      // Use the exact endpoint from FileManagement.md
      const response = await apiClient.get(`/cvs/${cvId}/files`);
      return response.files || [];
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch files');
    }
  }

  // Download file
  static async downloadFile(fileId: string): Promise<Blob> {
    try {
      // Use the exact endpoint from FileManagement.md
      const blob = await apiClient.getBinary(`/files/${fileId}`);
      return blob;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to download file');
    }
  }

  // Helper method to create download link
  static createDownloadLink(blob: Blob, filename: string): void {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }

  // Helper method to get file type icon
  static getFileTypeIcon(fileType: string): string {
    if (fileType.startsWith('image/')) return '🖼️';
    if (fileType === 'application/pdf') return '📄';
    if (fileType.startsWith('text/')) return '📝';
    return '📎';
  }

  // Helper method to format file size
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Helper method to validate image dimensions (for profile photos)
  static async validateImageDimensions(file: File, minWidth = 100, minHeight = 100): Promise<FileValidationResult> {
    return new Promise((resolve) => {
      if (!file.type.startsWith('image/')) {
        resolve({ isValid: true }); // Not an image, skip validation
        return;
      }

      const img = new Image();
      img.onload = () => {
        if (img.width < minWidth || img.height < minHeight) {
          resolve({
            isValid: false,
            error: `Image dimensions must be at least ${minWidth}x${minHeight} pixels`
          });
        } else {
          resolve({ isValid: true });
        }
      };
      img.onerror = () => {
        resolve({
          isValid: false,
          error: 'Invalid image file'
        });
      };
      img.src = URL.createObjectURL(file);
    });
  }
}

export default FileService;
