import { BASE_URL } from '@/lib/constants';
import { getAccessToken, getRefreshToken, setTokens, clearTokens, shouldRefreshToken } from '@/lib/auth';
import type { TokenResponse } from '@/types/auth';

export class APIError extends Error {
  constructor(
    message: string,
    public status: number,
    public code?: string
  ) {
    super(message);
    this.name = 'APIError';
  }
}

class APIClient {
  private baseURL: string;
  private isRefreshing: boolean = false;
  private refreshPromise: Promise<void> | null = null;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  private async refreshAccessToken(): Promise<void> {
    const refreshToken = getRefreshToken();
    if (!refreshToken) {
      throw new APIError('No refresh token available', 401);
    }

    const response = await fetch(`${this.baseURL}/auth/refresh-token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ refresh_token: refreshToken }),
    });

    if (!response.ok) {
      clearTokens();
      throw new APIError('Failed to refresh token', response.status);
    }

    const tokenData: TokenResponse = await response.json();
    setTokens(tokenData.access_token, tokenData.refresh_token);
  }

  private async handleTokenRefresh(): Promise<void> {
    if (this.isRefreshing) {
      // If already refreshing, wait for the existing refresh to complete
      if (this.refreshPromise) {
        await this.refreshPromise;
      }
      return;
    }

    this.isRefreshing = true;
    this.refreshPromise = this.refreshAccessToken();

    try {
      await this.refreshPromise;
    } finally {
      this.isRefreshing = false;
      this.refreshPromise = null;
    }
  }

  async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    // Check if we need to refresh the token before making the request
    if (shouldRefreshToken()) {
      try {
        await this.handleTokenRefresh();
      } catch (error) {
        // If refresh fails, redirect to login
        clearTokens();
        if (typeof window !== 'undefined') {
          window.location.href = '/auth/login';
        }
        throw error;
      }
    }

    const url = `${this.baseURL}${endpoint}`;
    const accessToken = getAccessToken();

    const config: RequestInit = {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
        ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
      },
    };

    let response = await fetch(url, config);

    // If we get a 401 and we have a refresh token, try to refresh and retry
    if (response.status === 401 && getRefreshToken()) {
      try {
        await this.handleTokenRefresh();
        
        // Retry the original request with the new token
        const newAccessToken = getAccessToken();
        const retryConfig: RequestInit = {
          ...config,
          headers: {
            ...config.headers,
            ...(newAccessToken && { Authorization: `Bearer ${newAccessToken}` }),
          },
        };
        
        response = await fetch(url, retryConfig);
      } catch (refreshError) {
        clearTokens();
        if (typeof window !== 'undefined') {
          window.location.href = '/auth/login';
        }
        throw refreshError;
      }
    }

    if (!response.ok) {
      let errorMessage = `HTTP ${response.status}`;
      let errorCode: string | undefined;

      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorData.detail || errorMessage;
        errorCode = errorData.code;
      } catch {
        // If we can't parse the error response, use the status text
        errorMessage = response.statusText || errorMessage;
      }

      throw new APIError(errorMessage, response.status, errorCode);
    }

    try {
      return await response.json();
    } catch {
      // If response is not JSON, return empty object
      return {} as T;
    }
  }

  // Convenience methods
  async get<T>(endpoint: string, options?: RequestInit): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'GET' });
  }

  async post<T>(endpoint: string, data?: any, options?: RequestInit): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: any, options?: RequestInit): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string, options?: RequestInit): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'DELETE' });
  }

  // File upload method
  async uploadFile<T>(endpoint: string, file: File, additionalData?: Record<string, string>): Promise<T> {
    const formData = new FormData();
    formData.append('file', file);

    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, value);
      });
    }

    const accessToken = getAccessToken();

    return this.request<T>(endpoint, {
      method: 'POST',
      headers: {
        ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
        // Don't set Content-Type for FormData, let the browser set it
      },
      body: formData,
    });
  }

  // FormData upload method
  async postFormData<T>(endpoint: string, formData: FormData, options?: RequestInit): Promise<T> {
    const accessToken = getAccessToken();

    return this.request<T>(endpoint, {
      ...options,
      method: 'POST',
      headers: {
        ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
        // Don't set Content-Type for FormData, let the browser set it
        ...options?.headers,
      },
      body: formData,
    });
  }

  // Binary data download method
  async getBinary(endpoint: string, options?: RequestInit): Promise<Blob> {
    const url = `${this.baseURL}${endpoint}`;
    const accessToken = getAccessToken();

    const config: RequestInit = {
      ...options,
      headers: {
        ...options?.headers,
        ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
      },
    };

    const response = await fetch(url, config);

    if (!response.ok) {
      let errorMessage = `HTTP ${response.status}`;
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorData.detail || errorMessage;
      } catch {
        errorMessage = response.statusText || errorMessage;
      }
      throw new APIError(errorMessage, response.status);
    }

    return await response.blob();
  }
}

// Create and export the API client instance
export const apiClient = new APIClient(BASE_URL);

// Export the APIError for use in components
export { APIError };
