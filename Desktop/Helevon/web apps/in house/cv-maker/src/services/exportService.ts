import { apiClient } from './api';

export interface ExportOptions {
  template_id?: string;
  include_certificates?: boolean;
  include_cover_letter?: boolean;
  primary_color?: string;
  format?: 'pdf' | 'docx' | 'html';
}

export interface ExportResponse {
  success: boolean;
  message: string;
  download_url?: string;
}

export class ExportService {
  // Export CV using the API endpoint from CVManagement.md
  static async exportCV(cvId: string, options: ExportOptions = {}): Promise<Blob> {
    try {
      // Set default options
      const exportOptions: ExportOptions = {
        template_id: options.template_id,
        include_certificates: options.include_certificates ?? true,
        include_cover_letter: options.include_cover_letter ?? true,
        primary_color: options.primary_color ?? '#0ea5e9',
        format: options.format ?? 'pdf',
        ...options
      };

      // Use the exact endpoint from CVManagement.md
      const response = await apiClient.post(`/cvs/${cvId}/export`, exportOptions);
      
      // If the API returns a download URL, fetch the file
      if (response.download_url) {
        const fileResponse = await fetch(response.download_url);
        return await fileResponse.blob();
      }
      
      // If the API returns binary data directly
      if (response instanceof Blob) {
        return response;
      }
      
      throw new Error('Invalid export response format');
    } catch (error: any) {
      // Fallback to mock PDF for development
      console.warn('Export API not available, generating mock PDF:', error.message);
      return this.generateMockPDF(cvId, options);
    }
  }

  // Generate a mock PDF for development/testing
  static generateMockPDF(cvId: string, options: ExportOptions): Blob {
    const mockPdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
/Resources <<
/Font <<
/F1 5 0 R
>>
>>
>>
endobj

4 0 obj
<<
/Length 200
>>
stream
BT
/F1 12 Tf
72 720 Td
(CV Export - Mock PDF) Tj
0 -20 Td
(CV ID: ${cvId}) Tj
0 -20 Td
(Template: ${options.template_id || 'default'}) Tj
0 -20 Td
(Format: ${options.format || 'pdf'}) Tj
0 -20 Td
(Include Certificates: ${options.include_certificates ? 'Yes' : 'No'}) Tj
0 -20 Td
(Include Cover Letter: ${options.include_cover_letter ? 'Yes' : 'No'}) Tj
0 -20 Td
(Primary Color: ${options.primary_color || '#0ea5e9'}) Tj
ET
endstream
endobj

5 0 obj
<<
/Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj

xref
0 6
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000244 00000 n 
0000000495 00000 n 
trailer
<<
/Size 6
/Root 1 0 R
>>
startxref
565
%%EOF`;

    return new Blob([mockPdfContent], { type: 'application/pdf' });
  }

  // Download exported CV
  static downloadCV(blob: Blob, filename: string): void {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }

  // Get export status (for async exports)
  static async getExportStatus(exportId: string): Promise<any> {
    try {
      const response = await apiClient.get(`/exports/${exportId}/status`);
      return response;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to get export status');
    }
  }

  // Get available templates for export
  static async getAvailableTemplates(): Promise<any[]> {
    try {
      const response = await apiClient.get('/templates');
      return response.templates || [];
    } catch (error: any) {
      // Return mock templates for development
      return [
        { id: 'modern', name: 'Modern Professional', category: 'professional' },
        { id: 'standard', name: 'Standard', category: 'professional' },
        { id: 'creative', name: 'Creative', category: 'creative' },
        { id: 'german', name: 'German Standard', category: 'professional' }
      ];
    }
  }

  // Validate export options
  static validateExportOptions(options: ExportOptions): { isValid: boolean; error?: string } {
    const validFormats = ['pdf', 'docx', 'html'];
    
    if (options.format && !validFormats.includes(options.format)) {
      return {
        isValid: false,
        error: `Invalid format. Supported formats: ${validFormats.join(', ')}`
      };
    }

    if (options.primary_color && !/^#[0-9A-F]{6}$/i.test(options.primary_color)) {
      return {
        isValid: false,
        error: 'Primary color must be a valid hex color (e.g., #0ea5e9)'
      };
    }

    return { isValid: true };
  }

  // Get export preview (smaller version for preview)
  static async getExportPreview(cvId: string, options: ExportOptions = {}): Promise<Blob> {
    try {
      const previewOptions = {
        ...options,
        format: 'pdf',
        preview: true // Add preview flag
      };

      const response = await apiClient.post(`/cvs/${cvId}/export/preview`, previewOptions);
      
      if (response instanceof Blob) {
        return response;
      }
      
      throw new Error('Invalid preview response format');
    } catch (error: any) {
      // Fallback to regular export for preview
      return this.exportCV(cvId, { ...options, format: 'pdf' });
    }
  }

  // Batch export multiple CVs
  static async batchExport(cvIds: string[], options: ExportOptions = {}): Promise<Blob> {
    try {
      const response = await apiClient.post('/cvs/batch-export', {
        cv_ids: cvIds,
        options
      });
      
      if (response instanceof Blob) {
        return response;
      }
      
      throw new Error('Invalid batch export response format');
    } catch (error: any) {
      throw new Error(error.message || 'Failed to batch export CVs');
    }
  }
}

export default ExportService;
