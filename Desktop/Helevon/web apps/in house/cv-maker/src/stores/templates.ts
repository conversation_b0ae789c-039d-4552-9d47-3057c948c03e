import { create } from 'zustand';
import { apiClient } from '@/services/api';

export interface CVTemplate {
  id: string;
  name: string;
  description: string;
  category: 'professional' | 'academic' | 'creative' | 'technical';
  languages: ('en' | 'de' | 'ar')[];
  features: string[];
  preview_url: string;
  is_premium: boolean;
  cultural_notes: {
    [key: string]: string;
  };
}

interface TemplateStore {
  templates: CVTemplate[];
  isLoading: boolean;
  error: string | null;
  
  // Actions
  fetchTemplates: () => Promise<void>;
  getTemplate: (id: string) => CVTemplate | undefined;
  getTemplatesByLanguage: (language: 'en' | 'de' | 'ar') => CVTemplate[];
  getTemplatesByCategory: (category: string) => CVTemplate[];
}

export const useTemplateStore = create<TemplateStore>((set, get) => ({
  // State
  templates: [],
  isLoading: false,
  error: null,

  // Actions
  fetchTemplates: async () => {
    set({ isLoading: true, error: null });
    
    try {
      // For now, we'll use mock data since the API might not be ready
      // In production, this would be: const templates = await apiClient.get<CVTemplate[]>('/templates');
      
      const mockTemplates: CVTemplate[] = [
        {
          id: 'modern-professional',
          name: 'Modern Professional',
          description: 'Clean and contemporary design perfect for business professionals',
          category: 'professional',
          languages: ['en', 'de', 'ar'],
          features: ['ATS-Friendly', 'Photo Optional', 'Skills Section'],
          preview_url: '/templates/modern-professional/preview.jpg',
          is_premium: false,
          cultural_notes: {
            en: 'International standard format',
            de: 'Adaptable for German market with photo section',
            ar: 'RTL layout compatible'
          }
        },
        {
          id: 'german-standard',
          name: 'German Standard',
          description: 'Traditional German CV format with photo and personal details',
          category: 'professional',
          languages: ['de', 'en'],
          features: ['Photo Required', 'Personal Details', 'Chronological'],
          preview_url: '/templates/german-standard/preview.jpg',
          is_premium: false,
          cultural_notes: {
            de: 'Standard German Lebenslauf format',
            en: 'German-style format for European applications'
          }
        },
        {
          id: 'arabic-elegant',
          name: 'Arabic Elegant',
          description: 'Beautiful RTL design optimized for Arabic content',
          category: 'professional',
          languages: ['ar', 'en'],
          features: ['RTL Layout', 'Arabic Typography', 'Cultural Sections'],
          preview_url: '/templates/arabic-elegant/preview.jpg',
          is_premium: true,
          cultural_notes: {
            ar: 'تصميم أنيق للسيرة الذاتية العربية',
            en: 'Arabic-optimized design with RTL support'
          }
        },
        {
          id: 'creative-portfolio',
          name: 'Creative Portfolio',
          description: 'Vibrant design for creative professionals and artists',
          category: 'creative',
          languages: ['en', 'de'],
          features: ['Portfolio Section', 'Color Customization', 'Visual Focus'],
          preview_url: '/templates/creative-portfolio/preview.jpg',
          is_premium: true,
          cultural_notes: {
            en: 'Perfect for creative industries',
            de: 'Ideal for creative professionals in German market'
          }
        },
        {
          id: 'academic-research',
          name: 'Academic Research',
          description: 'Comprehensive format for academic and research positions',
          category: 'academic',
          languages: ['en', 'de'],
          features: ['Publications Section', 'Research Focus', 'Detailed Format'],
          preview_url: '/templates/academic-research/preview.jpg',
          is_premium: false,
          cultural_notes: {
            en: 'Standard academic CV format',
            de: 'German academic standards compliant'
          }
        },
        {
          id: 'tech-minimal',
          name: 'Tech Minimal',
          description: 'Clean, minimal design for tech professionals',
          category: 'technical',
          languages: ['en', 'de'],
          features: ['Skills Matrix', 'Project Showcase', 'GitHub Integration'],
          preview_url: '/templates/tech-minimal/preview.jpg',
          is_premium: false,
          cultural_notes: {
            en: 'Perfect for software developers',
            de: 'Tech-focused format for German IT market'
          }
        }
      ];

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      set({ templates: mockTemplates, isLoading: false });
    } catch (error: any) {
      set({ 
        error: error.message || 'Failed to fetch templates', 
        isLoading: false 
      });
      throw error;
    }
  },

  getTemplate: (id: string) => {
    const { templates } = get();
    return templates.find(template => template.id === id);
  },

  getTemplatesByLanguage: (language: 'en' | 'de' | 'ar') => {
    const { templates } = get();
    return templates.filter(template => template.languages.includes(language));
  },

  getTemplatesByCategory: (category: string) => {
    const { templates } = get();
    if (category === 'all') return templates;
    return templates.filter(template => template.category === category);
  }
}));
