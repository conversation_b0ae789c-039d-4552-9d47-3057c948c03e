import { create } from 'zustand';
import { apiClient } from '@/services/api';
import type { 
  CVStore,
  CVListItem,
  CVWithFiles,
  CreateCVPayload,
  UpdateCVPayload,
  PersonalInfo,
  Education,
  WorkExperience,
  Skill,
  Reference,
  CoverLetter
} from '@/types/cv';

export const useCVStore = create<CVStore>((set, get) => ({
  // State
  cvs: [],
  currentCV: null,
  isLoading: false,
  error: null,

  // Actions
  fetchCVs: async () => {
    set({ isLoading: true, error: null });
    
    try {
      const cvs = await apiClient.get<CVListItem[]>('/cv');
      set({ cvs, isLoading: false });
    } catch (error: any) {
      set({ 
        error: error.message || 'Failed to fetch CVs', 
        isLoading: false 
      });
      throw error;
    }
  },

  fetchCV: async (id: string) => {
    set({ isLoading: true, error: null });
    
    try {
      const cv = await apiClient.get<CVWithFiles>(`/cv/${id}`);
      set({ currentCV: cv, isLoading: false });
    } catch (error: any) {
      set({ 
        error: error.message || 'Failed to fetch CV', 
        isLoading: false 
      });
      throw error;
    }
  },

  createCV: async (data: CreateCVPayload): Promise<string> => {
    set({ isLoading: true, error: null });
    
    try {
      const cv = await apiClient.post<CVWithFiles>('/cv', data);
      
      // Add the new CV to the list
      const currentCVs = get().cvs;
      const newCVListItem: CVListItem = {
        id: cv.id,
        user_id: cv.user_id,
        title: cv.title,
        template: cv.template,
        language: cv.language,
        created_at: cv.created_at,
        updated_at: cv.updated_at
      };
      
      set({ 
        cvs: [newCVListItem, ...currentCVs],
        currentCV: cv,
        isLoading: false 
      });
      
      return cv.id;
    } catch (error: any) {
      set({ 
        error: error.message || 'Failed to create CV', 
        isLoading: false 
      });
      throw error;
    }
  },

  updateCV: async (id: string, data: UpdateCVPayload) => {
    set({ isLoading: true, error: null });
    
    try {
      const updatedCV = await apiClient.put<CVWithFiles>(`/cv/${id}`, data);
      
      // Update the CV in the list
      const currentCVs = get().cvs;
      const updatedCVs = currentCVs.map(cv => 
        cv.id === id 
          ? { ...cv, ...data, updated_at: updatedCV.updated_at }
          : cv
      );
      
      set({ 
        cvs: updatedCVs,
        currentCV: updatedCV,
        isLoading: false 
      });
    } catch (error: any) {
      set({ 
        error: error.message || 'Failed to update CV', 
        isLoading: false 
      });
      throw error;
    }
  },

  deleteCV: async (id: string) => {
    set({ isLoading: true, error: null });
    
    try {
      await apiClient.delete(`/cv/${id}`);
      
      // Remove the CV from the list
      const currentCVs = get().cvs;
      const filteredCVs = currentCVs.filter(cv => cv.id !== id);
      
      set({ 
        cvs: filteredCVs,
        currentCV: get().currentCV?.id === id ? null : get().currentCV,
        isLoading: false 
      });
    } catch (error: any) {
      set({ 
        error: error.message || 'Failed to delete CV', 
        isLoading: false 
      });
      throw error;
    }
  },

  updatePersonalInfo: async (cvId: string, data: PersonalInfo) => {
    set({ isLoading: true, error: null });
    
    try {
      const updatedCV = await apiClient.put<CVWithFiles>(`/cv/${cvId}/personal-info`, data);
      set({ currentCV: updatedCV, isLoading: false });
    } catch (error: any) {
      set({ 
        error: error.message || 'Failed to update personal info', 
        isLoading: false 
      });
      throw error;
    }
  },

  updateEducation: async (cvId: string, data: { education: Education[] }) => {
    set({ isLoading: true, error: null });
    
    try {
      const updatedCV = await apiClient.put<CVWithFiles>(`/cv/${cvId}/education`, data);
      set({ currentCV: updatedCV, isLoading: false });
    } catch (error: any) {
      set({ 
        error: error.message || 'Failed to update education', 
        isLoading: false 
      });
      throw error;
    }
  },

  updateWorkExperience: async (cvId: string, data: { work_experience: WorkExperience[] }) => {
    set({ isLoading: true, error: null });
    
    try {
      const updatedCV = await apiClient.put<CVWithFiles>(`/cv/${cvId}/work-experience`, data);
      set({ currentCV: updatedCV, isLoading: false });
    } catch (error: any) {
      set({ 
        error: error.message || 'Failed to update work experience', 
        isLoading: false 
      });
      throw error;
    }
  },

  updateSkills: async (cvId: string, data: { skills: Skill[] }) => {
    set({ isLoading: true, error: null });
    
    try {
      const updatedCV = await apiClient.put<CVWithFiles>(`/cv/${cvId}/skills`, data);
      set({ currentCV: updatedCV, isLoading: false });
    } catch (error: any) {
      set({ 
        error: error.message || 'Failed to update skills', 
        isLoading: false 
      });
      throw error;
    }
  },

  updateReferences: async (cvId: string, data: { references: Reference[] }) => {
    set({ isLoading: true, error: null });
    
    try {
      const updatedCV = await apiClient.put<CVWithFiles>(`/cv/${cvId}/references`, data);
      set({ currentCV: updatedCV, isLoading: false });
    } catch (error: any) {
      set({ 
        error: error.message || 'Failed to update references', 
        isLoading: false 
      });
      throw error;
    }
  },

  updateCoverLetter: async (cvId: string, data: CoverLetter) => {
    set({ isLoading: true, error: null });
    
    try {
      const updatedCV = await apiClient.put<CVWithFiles>(`/cv/${cvId}/cover-letter`, data);
      set({ currentCV: updatedCV, isLoading: false });
    } catch (error: any) {
      set({ 
        error: error.message || 'Failed to update cover letter', 
        isLoading: false 
      });
      throw error;
    }
  },

  clearError: () => {
    set({ error: null });
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },
}));
