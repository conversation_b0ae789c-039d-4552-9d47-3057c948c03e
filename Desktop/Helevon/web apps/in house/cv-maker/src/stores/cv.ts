import { create } from 'zustand';
import { apiClient } from '@/services/api';
import type {
  CVStore,
  CVListItem,
  CVWithFiles,
  CreateCVPayload,
  UpdateCVPayload,
  PersonalInfo,
  Education,
  WorkExperience,
  Skill,
  Reference,
  CoverLetter
} from '@/types/cv';

export const useCVStore = create<CVStore>((set, get) => ({
  // State
  cvs: [],
  currentCV: null,
  isLoading: false,
  error: null,

  // Actions
  fetchCVs: async () => {
    set({ isLoading: true, error: null });
    
    try {
      const cvs = await apiClient.get<CVListItem[]>('/cv');
      set({ cvs, isLoading: false });
    } catch (error: any) {
      set({ 
        error: error.message || 'Failed to fetch CVs', 
        isLoading: false 
      });
      throw error;
    }
  },

  fetchCV: async (id: string) => {
    set({ isLoading: true, error: null });

    try {
      // Use the exact endpoint from CVManagement.md: GET /cv/{cv_id}
      const cv = await apiClient.get<CVWithFiles>(`/cv/${id}`);
      set({ currentCV: cv, isLoading: false });
    } catch (error: any) {
      // Fallback to mock data for development - matching CVWithFiles structure exactly
      console.warn('API not available, using mock CV data:', error.message);

      const mockCV: CVWithFiles = {
        created_at: '2024-01-15T10:00:00Z',
        updated_at: '2024-01-15T10:00:00Z',
        title: 'My Professional CV',
        template: 'modern',
        language: 'en',
        id,
        user_id: 'user1',
        personal_info: {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '+1234567890',
          address: '123 Main St',
          city: 'New York',
          postalCode: '10001',
          country: 'USA',
          dateOfBirth: '1990-01-01',
          placeOfBirth: 'New York',
          nationality: 'American',
          maritalStatus: 'single',
          summary: 'Experienced professional with a passion for excellence.'
        },
        education: [
          {
            id: '1',
            institution: 'University of Technology',
            degree: 'Bachelor of Science',
            fieldOfStudy: 'Computer Science',
            startDate: '2018-09-01',
            endDate: '2022-06-01',
            isCurrentlyStudying: false,
            grade: '3.8 GPA',
            description: 'Focused on software engineering and data structures.',
            certificates: []
          }
        ],
        work_experience: [
          {
            id: '1',
            company: 'Tech Solutions Inc.',
            position: 'Software Developer',
            startDate: '2022-07-01',
            endDate: '2024-01-01',
            isCurrentlyWorking: false,
            description: 'Developed web applications using modern technologies.',
            location: 'New York, NY'
          }
        ],
        skills: [
          {
            id: '1',
            name: 'JavaScript',
            category: 'technical',
            level: 'advanced'
          },
          {
            id: '2',
            name: 'English',
            category: 'language',
            level: 'native'
          }
        ],
        references: [
          {
            id: '1',
            name: 'Jane Smith',
            position: 'Senior Manager',
            company: 'Tech Solutions Inc.',
            email: '<EMAIL>',
            phone: '+1234567891'
          }
        ],
        cover_letter: {
          recipientName: 'Hiring Manager',
          company: 'Target Company',
          address: '456 Business Ave',
          postalCode: '10002',
          city: 'New York',
          country: 'USA',
          email: '<EMAIL>',
          phone: '+1234567892',
          otherInformation: '',
          subject: 'Application for Software Developer Position',
          date: '2024-01-15',
          content: 'Dear Hiring Manager, I am writing to express my interest in the Software Developer position at your company. With my background in computer science and hands-on experience in web development, I am confident I would be a valuable addition to your team.',
          signatureFileId: ''
        },
        files: []
      };

      await new Promise(resolve => setTimeout(resolve, 500));
      set({ currentCV: mockCV, isLoading: false });
    }
  },

  createCV: async (data: CreateCVPayload): Promise<string> => {
    set({ isLoading: true, error: null });
    
    try {
      const cv = await apiClient.post<CVWithFiles>('/cv', data);
      
      // Add the new CV to the list
      const currentCVs = get().cvs;
      const newCVListItem: CVListItem = {
        id: cv.id,
        user_id: cv.user_id,
        title: cv.title,
        template: cv.template,
        language: cv.language,
        created_at: cv.created_at,
        updated_at: cv.updated_at
      };
      
      set({ 
        cvs: [newCVListItem, ...currentCVs],
        currentCV: cv,
        isLoading: false 
      });
      
      return cv.id;
    } catch (error: any) {
      set({ 
        error: error.message || 'Failed to create CV', 
        isLoading: false 
      });
      throw error;
    }
  },

  updateCV: async (id: string, data: UpdateCVPayload) => {
    set({ isLoading: true, error: null });
    
    try {
      const updatedCV = await apiClient.put<CVWithFiles>(`/cv/${id}`, data);
      
      // Update the CV in the list
      const currentCVs = get().cvs;
      const updatedCVs = currentCVs.map(cv => 
        cv.id === id 
          ? { ...cv, ...data, updated_at: updatedCV.updated_at }
          : cv
      );
      
      set({ 
        cvs: updatedCVs,
        currentCV: updatedCV,
        isLoading: false 
      });
    } catch (error: any) {
      set({ 
        error: error.message || 'Failed to update CV', 
        isLoading: false 
      });
      throw error;
    }
  },

  deleteCV: async (id: string) => {
    set({ isLoading: true, error: null });
    
    try {
      await apiClient.delete(`/cv/${id}`);
      
      // Remove the CV from the list
      const currentCVs = get().cvs;
      const filteredCVs = currentCVs.filter(cv => cv.id !== id);
      
      set({ 
        cvs: filteredCVs,
        currentCV: get().currentCV?.id === id ? null : get().currentCV,
        isLoading: false 
      });
    } catch (error: any) {
      set({ 
        error: error.message || 'Failed to delete CV', 
        isLoading: false 
      });
      throw error;
    }
  },

  updatePersonalInfo: async (cvId: string, data: PersonalInfo) => {
    set({ isLoading: true, error: null });
    
    try {
      const updatedCV = await apiClient.put<CVWithFiles>(`/cv/${cvId}/personal-info`, data);
      set({ currentCV: updatedCV, isLoading: false });
    } catch (error: any) {
      set({ 
        error: error.message || 'Failed to update personal info', 
        isLoading: false 
      });
      throw error;
    }
  },

  updateEducation: async (cvId: string, data: { education: Education[] }) => {
    set({ isLoading: true, error: null });
    
    try {
      const updatedCV = await apiClient.put<CVWithFiles>(`/cv/${cvId}/education`, data);
      set({ currentCV: updatedCV, isLoading: false });
    } catch (error: any) {
      set({ 
        error: error.message || 'Failed to update education', 
        isLoading: false 
      });
      throw error;
    }
  },

  updateWorkExperience: async (cvId: string, data: { work_experience: WorkExperience[] }) => {
    set({ isLoading: true, error: null });
    
    try {
      const updatedCV = await apiClient.put<CVWithFiles>(`/cv/${cvId}/work-experience`, data);
      set({ currentCV: updatedCV, isLoading: false });
    } catch (error: any) {
      set({ 
        error: error.message || 'Failed to update work experience', 
        isLoading: false 
      });
      throw error;
    }
  },

  updateSkills: async (cvId: string, data: { skills: Skill[] }) => {
    set({ isLoading: true, error: null });
    
    try {
      const updatedCV = await apiClient.put<CVWithFiles>(`/cv/${cvId}/skills`, data);
      set({ currentCV: updatedCV, isLoading: false });
    } catch (error: any) {
      set({ 
        error: error.message || 'Failed to update skills', 
        isLoading: false 
      });
      throw error;
    }
  },

  updateReferences: async (cvId: string, data: { references: Reference[] }) => {
    set({ isLoading: true, error: null });
    
    try {
      const updatedCV = await apiClient.put<CVWithFiles>(`/cv/${cvId}/references`, data);
      set({ currentCV: updatedCV, isLoading: false });
    } catch (error: any) {
      set({ 
        error: error.message || 'Failed to update references', 
        isLoading: false 
      });
      throw error;
    }
  },

  updateCoverLetter: async (cvId: string, data: CoverLetter) => {
    set({ isLoading: true, error: null });
    
    try {
      const updatedCV = await apiClient.put<CVWithFiles>(`/cv/${cvId}/cover-letter`, data);
      set({ currentCV: updatedCV, isLoading: false });
    } catch (error: any) {
      set({ 
        error: error.message || 'Failed to update cover letter', 
        isLoading: false 
      });
      throw error;
    }
  },

  clearError: () => {
    set({ error: null });
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  // Export CV - Based on CVManagement.md
  exportCV: async (cvId: string, options?: any) => {
    try {
      // Use the exact endpoint from CVManagement.md: GET /cv/{cv_id}/export
      // Note: Options would be passed as query parameters in a real implementation
      const blob = await apiClient.getBinary(`/cv/${cvId}/export`);
      return blob;
    } catch (error: any) {
      // Mock PDF generation for development
      console.warn('Export API not available, generating mock PDF:', error.message);

      // Create a simple mock PDF blob
      const mockPdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
72 720 Td
(CV Preview - Mock PDF) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000206 00000 n
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
299
%%EOF`;

      return new Blob([mockPdfContent], { type: 'application/pdf' });
    }
  },

  // File Upload - Based on FileManagement.md
  uploadFile: async (cvId: string, file: File, category: string) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('category', category);

      // Use the exact endpoint from FileManagement.md: POST /cv/{cv_id}/upload
      const response = await apiClient.postFormData<any>(`/cv/${cvId}/upload`, formData);

      // Update current CV with new file
      const currentCV = get().currentCV;
      if (currentCV) {
        set({
          currentCV: {
            ...currentCV,
            files: [...currentCV.files, response]
          }
        });
      }

      return response;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to upload file');
    }
  },

  // Delete File - Based on FileManagement.md
  deleteFile: async (fileId: string) => {
    try {
      // Use the exact endpoint from FileManagement.md: DELETE /cv/files/{file_id}
      await apiClient.delete(`/cv/files/${fileId}`);

      // Remove file from current CV
      const currentCV = get().currentCV;
      if (currentCV) {
        set({
          currentCV: {
            ...currentCV,
            files: currentCV.files.filter((file: any) => file.id !== fileId)
          }
        });
      }
    } catch (error: any) {
      throw new Error(error.message || 'Failed to delete file');
    }
  },
}));
