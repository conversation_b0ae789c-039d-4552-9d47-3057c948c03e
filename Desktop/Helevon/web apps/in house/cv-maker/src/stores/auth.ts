import { create } from 'zustand';
import { apiClient } from '@/services/api';
import { setTokens, clearTokens, isAuthenticated } from '@/lib/auth';
import type { 
  AuthStore, 
  LoginPayload, 
  RegisterPayload, 
  TokenResponse, 
  UserResponse, 
  UserUpdatePayload,
  UserProfile 
} from '@/types/auth';

export const useAuthStore = create<AuthStore>((set, get) => ({
  // State
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,

  // Actions
  login: async (credentials: LoginPayload) => {
    set({ isLoading: true, error: null });
    
    try {
      const tokenResponse = await apiClient.post<TokenResponse>('/auth/signin', credentials);
      setTokens(tokenResponse.access_token, tokenResponse.refresh_token);
      
      // Fetch user data after successful login
      await get().fetchUserAccount();
      
      set({ isAuthenticated: true, isLoading: false });
    } catch (error: any) {
      set({ 
        error: error.message || 'Login failed', 
        isLoading: false,
        isAuthenticated: false,
        user: null
      });
      throw error;
    }
  },

  register: async (userData: RegisterPayload) => {
    set({ isLoading: true, error: null });
    
    try {
      const userResponse = await apiClient.post<UserResponse>('/auth/register', userData);
      
      // After successful registration, automatically log in
      await get().login({
        email: userData.email,
        password: userData.password
      });
      
      set({ isLoading: false });
    } catch (error: any) {
      set({ 
        error: error.message || 'Registration failed', 
        isLoading: false 
      });
      throw error;
    }
  },

  logout: async () => {
    set({ isLoading: true });
    
    try {
      // Call the logout endpoint to invalidate the refresh token
      await apiClient.post('/auth/signout');
    } catch (error) {
      // Even if the API call fails, we should still clear local tokens
      console.error('Logout API call failed:', error);
    } finally {
      clearTokens();
      set({ 
        user: null, 
        isAuthenticated: false, 
        isLoading: false, 
        error: null 
      });
    }
  },

  refreshToken: async () => {
    set({ isLoading: true, error: null });
    
    try {
      // The API client handles token refresh automatically
      // We just need to fetch the user data again
      await get().fetchUserAccount();
      set({ isAuthenticated: true, isLoading: false });
    } catch (error: any) {
      clearTokens();
      set({ 
        user: null, 
        isAuthenticated: false, 
        isLoading: false,
        error: error.message || 'Token refresh failed'
      });
      throw error;
    }
  },

  updateAccount: async (userData: UserUpdatePayload) => {
    set({ isLoading: true, error: null });
    
    try {
      const updatedUser = await apiClient.put<UserResponse>('/user/account', userData);
      set({ user: updatedUser, isLoading: false });
    } catch (error: any) {
      set({ 
        error: error.message || 'Account update failed', 
        isLoading: false 
      });
      throw error;
    }
  },

  fetchUserAccount: async () => {
    set({ isLoading: true, error: null });
    
    try {
      const user = await apiClient.get<UserResponse>('/user/account');
      set({ user, isAuthenticated: true, isLoading: false });
    } catch (error: any) {
      set({ 
        error: error.message || 'Failed to fetch user account', 
        isLoading: false,
        isAuthenticated: false,
        user: null
      });
      throw error;
    }
  },

  fetchUserProfile: async (): Promise<UserProfile> => {
    set({ isLoading: true, error: null });
    
    try {
      const profile = await apiClient.get<UserProfile>('/user/profile');
      set({ isLoading: false });
      return profile;
    } catch (error: any) {
      set({ 
        error: error.message || 'Failed to fetch user profile', 
        isLoading: false 
      });
      throw error;
    }
  },

  clearError: () => {
    set({ error: null });
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },
}));

// Initialize authentication state on app load
if (typeof window !== 'undefined') {
  const initializeAuth = async () => {
    if (isAuthenticated()) {
      try {
        await useAuthStore.getState().fetchUserAccount();
      } catch (error) {
        // If fetching user fails, clear tokens and reset state
        clearTokens();
        useAuthStore.setState({ 
          user: null, 
          isAuthenticated: false, 
          error: null 
        });
      }
    }
  };

  initializeAuth();
}
