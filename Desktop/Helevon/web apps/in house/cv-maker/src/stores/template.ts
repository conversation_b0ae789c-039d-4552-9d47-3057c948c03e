import { create } from 'zustand';
import { apiClient } from '@/services/api';
import type { TemplateStore, Template } from '@/types/template';

export const useTemplateStore = create<TemplateStore>((set, get) => ({
  // State
  templates: [],
  isLoading: false,
  error: null,

  // Actions
  fetchTemplates: async () => {
    set({ isLoading: true, error: null });
    
    try {
      const templates = await apiClient.get<Template[]>('/templates');
      set({ templates, isLoading: false });
    } catch (error: any) {
      set({ 
        error: error.message || 'Failed to fetch templates', 
        isLoading: false 
      });
      throw error;
    }
  },

  getTemplatePreview: async (templateId: string): Promise<string> => {
    try {
      // This endpoint returns the preview image URL or blob
      const response = await apiClient.get<{ preview_url: string }>(`/templates/${templateId}/preview`);
      return response.preview_url;
    } catch (error: any) {
      console.error('Failed to get template preview:', error);
      throw error;
    }
  },

  clearError: () => {
    set({ error: null });
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },
}));
