'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { User, LogOut, Settings, Plus, Globe } from 'lucide-react';
import { useAuthStore } from '@/stores/auth';
import { Button } from '@/components/ui/Button';
import { Dropdown } from '@/components/ui/Dropdown';
import { ROUTES, SUPPORTED_LANGUAGES } from '@/lib/constants';
import { cn } from '@/lib/utils';

export const Header: React.FC = () => {
  const router = useRouter();
  const { user, isAuthenticated, logout } = useAuthStore();
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);

  const handleLogout = async () => {
    try {
      await logout();
      router.push(ROUTES.HOME);
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const languageOptions = Object.entries(SUPPORTED_LANGUAGES).map(([code, name]) => ({
    value: code,
    label: name,
    icon: <Globe className="h-4 w-4" />,
  }));

  return (
    <header className="sticky top-0 z-40 w-full border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto flex h-16 items-center justify-between px-4">
        {/* Logo */}
        <Link href={ROUTES.HOME} className="flex items-center space-x-2">
          <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
            <span className="text-primary-foreground font-bold text-lg">CV</span>
          </div>
          <span className="font-bold text-xl">CV Maker</span>
        </Link>

        {/* Navigation */}
        <nav className="hidden md:flex items-center space-x-6">
          {isAuthenticated ? (
            <>
              <Link
                href={ROUTES.DASHBOARD}
                className="text-sm font-medium transition-colors hover:text-primary"
              >
                My CVs
              </Link>
              <Link
                href="/templates"
                className="text-sm font-medium transition-colors hover:text-primary"
              >
                Templates
              </Link>
            </>
          ) : (
            <>
              <Link
                href="#features"
                className="text-sm font-medium transition-colors hover:text-primary"
              >
                Features
              </Link>
              <Link
                href="#how-it-works"
                className="text-sm font-medium transition-colors hover:text-primary"
              >
                How It Works
              </Link>
            </>
          )}
        </nav>

        {/* Right side actions */}
        <div className="flex items-center space-x-4">
          {/* Language Selector */}
          <Dropdown
            options={languageOptions}
            value={user?.language || 'en'}
            placeholder="Language"
            className="w-32"
            onChange={(language) => {
              // TODO: Implement language change
              console.log('Language changed to:', language);
            }}
          />

          {isAuthenticated ? (
            <>
              {/* Create New CV Button */}
              <Button
                onClick={() => router.push(ROUTES.CV_CREATE)}
                className="hidden sm:flex"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create CV
              </Button>

              {/* User Menu */}
              <div className="relative">
                <button
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="flex items-center space-x-2 rounded-full bg-secondary p-2 hover:bg-secondary/80 transition-colors"
                >
                  <div className="h-8 w-8 rounded-full bg-primary flex items-center justify-center">
                    <User className="h-4 w-4 text-primary-foreground" />
                  </div>
                  <span className="hidden sm:block text-sm font-medium">
                    {user?.name}
                  </span>
                </button>

                {isUserMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 rounded-md border border-border bg-background shadow-lg">
                    <div className="py-1">
                      <div className="px-4 py-2 border-b border-border">
                        <p className="text-sm font-medium">{user?.name}</p>
                        <p className="text-xs text-muted">{user?.email}</p>
                      </div>
                      
                      <Link
                        href={ROUTES.PROFILE}
                        className="flex items-center px-4 py-2 text-sm hover:bg-accent hover:text-accent-foreground"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        <Settings className="h-4 w-4 mr-2" />
                        Profile Settings
                      </Link>
                      
                      <button
                        onClick={() => {
                          setIsUserMenuOpen(false);
                          handleLogout();
                        }}
                        className="flex w-full items-center px-4 py-2 text-sm hover:bg-accent hover:text-accent-foreground"
                      >
                        <LogOut className="h-4 w-4 mr-2" />
                        Sign Out
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </>
          ) : (
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                onClick={() => router.push(ROUTES.LOGIN)}
              >
                Sign In
              </Button>
              <Button
                onClick={() => router.push(ROUTES.REGISTER)}
              >
                Get Started
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Mobile Navigation */}
      {isAuthenticated && (
        <div className="sm:hidden border-t border-border bg-background">
          <div className="container mx-auto px-4 py-2">
            <Button
              onClick={() => router.push(ROUTES.CV_CREATE)}
              className="w-full"
              size="sm"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create New CV
            </Button>
          </div>
        </div>
      )}
    </header>
  );
};
