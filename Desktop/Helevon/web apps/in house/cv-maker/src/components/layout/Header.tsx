'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { User, LogOut, Settings, Plus, Globe, Sparkles } from 'lucide-react';
import { useAuthStore } from '@/stores/auth';
import { Button } from '@/components/ui/Button';
import { Dropdown } from '@/components/ui/Dropdown';
import { SimpleThemeToggle } from '@/components/ui/ThemeToggle';
import { MagneticElement } from '@/components/motion/MotionComponents';
import { ROUTES, SUPPORTED_LANGUAGES } from '@/lib/constants';
import { cn } from '@/lib/utils';

export const Header: React.FC = () => {
  const router = useRouter();
  const { user, isAuthenticated, logout } = useAuthStore();
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleLogout = async () => {
    try {
      await logout();
      router.push(ROUTES.HOME);
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const languageOptions = Object.entries(SUPPORTED_LANGUAGES).map(([code, name]) => ({
    value: code,
    label: name,
    icon: <Globe className="h-4 w-4" />,
  }));

  return (
    <motion.header
      className={cn(
        "sticky top-0 z-50 w-full transition-all duration-300",
        scrolled
          ? "glass border-b border-white/10 shadow-lg"
          : "bg-background/80 backdrop-blur-sm border-b border-border/50"
      )}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.5, ease: [0.4, 0, 0.2, 1] }}
    >
      <div className="container mx-auto flex h-16 items-center justify-between px-4">
        {/* Logo */}
        <MagneticElement strength={10}>
          <Link href={ROUTES.HOME} className="flex items-center space-x-3 group">
            <motion.div
              className="relative h-10 w-10 rounded-xl bg-gradient-to-r from-primary to-accent flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow"
              whileHover={{ scale: 1.05, rotate: 5 }}
              whileTap={{ scale: 0.95 }}
            >
              <span className="text-white font-bold text-lg">CV</span>
              <motion.div
                className="absolute -top-1 -right-1 w-3 h-3 bg-accent rounded-full"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              />
            </motion.div>
            <motion.span
              className="font-bold text-xl gradient-text bg-gradient-to-r from-primary to-accent bg-[length:200%_200%] animate-gradient"
              whileHover={{ scale: 1.02 }}
            >
              CV Maker
            </motion.span>
          </Link>
        </MagneticElement>

        {/* Navigation */}
        <nav className="hidden md:flex items-center space-x-8">
          {isAuthenticated ? (
            <>
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Link
                  href={ROUTES.DASHBOARD}
                  className="relative text-sm font-medium transition-colors hover:text-primary group"
                >
                  My CVs
                  <motion.div
                    className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-primary to-accent group-hover:w-full transition-all duration-300"
                  />
                </Link>
              </motion.div>
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Link
                  href="/templates"
                  className="relative text-sm font-medium transition-colors hover:text-primary group"
                >
                  Templates
                  <motion.div
                    className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-primary to-accent group-hover:w-full transition-all duration-300"
                  />
                </Link>
              </motion.div>
            </>
          ) : (
            <>
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Link
                  href="#features"
                  className="relative text-sm font-medium transition-colors hover:text-primary group"
                >
                  Features
                  <motion.div
                    className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-primary to-accent group-hover:w-full transition-all duration-300"
                  />
                </Link>
              </motion.div>
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Link
                  href="#how-it-works"
                  className="relative text-sm font-medium transition-colors hover:text-primary group"
                >
                  How It Works
                  <motion.div
                    className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-primary to-accent group-hover:w-full transition-all duration-300"
                  />
                </Link>
              </motion.div>
            </>
          )}
        </nav>

        {/* Right side actions */}
        <div className="flex items-center space-x-4">
          {/* Theme Toggle */}
          <SimpleThemeToggle />

          {/* Language Selector */}
          <Dropdown
            options={languageOptions}
            value={user?.language || 'en'}
            placeholder="Language"
            className="w-32"
            onChange={(language) => {
              // TODO: Implement language change
              console.log('Language changed to:', language);
            }}
          />

          {isAuthenticated ? (
            <>
              {/* Create New CV Button */}
              <MagneticElement>
                <Button
                  onClick={() => router.push(ROUTES.CV_CREATE)}
                  className="hidden sm:flex group"
                  gradient
                  glow
                >
                  <Plus className="h-4 w-4 mr-2 group-hover:rotate-90 transition-transform" />
                  Create CV
                  <Sparkles className="h-4 w-4 ml-2 group-hover:animate-pulse" />
                </Button>
              </MagneticElement>

              {/* User Menu */}
              <div className="relative">
                <motion.button
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="flex items-center space-x-2 rounded-full bg-gradient-to-r from-secondary to-secondary-dark p-2 hover:from-primary/10 hover:to-accent/10 transition-all duration-300 border border-border hover:border-primary/20"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <motion.div
                    className="h-8 w-8 rounded-full bg-gradient-to-r from-primary to-accent flex items-center justify-center shadow-lg"
                    animate={{ rotate: isUserMenuOpen ? 180 : 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <User className="h-4 w-4 text-white" />
                  </motion.div>
                  <span className="hidden sm:block text-sm font-medium">
                    {user?.name}
                  </span>
                </motion.button>

                <AnimatePresence>
                  {isUserMenuOpen && (
                    <motion.div
                      className="absolute right-0 mt-2 w-48 rounded-2xl border border-border glass shadow-xl overflow-hidden"
                      initial={{ opacity: 0, scale: 0.95, y: -10 }}
                      animate={{ opacity: 1, scale: 1, y: 0 }}
                      exit={{ opacity: 0, scale: 0.95, y: -10 }}
                      transition={{ duration: 0.2 }}
                    >
                      <div className="py-2">
                        <div className="px-4 py-3 border-b border-border/50">
                          <p className="text-sm font-medium">{user?.name}</p>
                          <p className="text-xs text-foreground-muted">{user?.email}</p>
                        </div>

                        <motion.div whileHover={{ x: 4 }} transition={{ duration: 0.2 }}>
                          <Link
                            href={ROUTES.PROFILE}
                            className="flex items-center px-4 py-3 text-sm hover:bg-primary/5 hover:text-primary transition-colors"
                            onClick={() => setIsUserMenuOpen(false)}
                          >
                            <Settings className="h-4 w-4 mr-3" />
                            Profile Settings
                          </Link>
                        </motion.div>

                        <motion.div whileHover={{ x: 4 }} transition={{ duration: 0.2 }}>
                          <button
                            onClick={() => {
                              setIsUserMenuOpen(false);
                              handleLogout();
                            }}
                            className="flex w-full items-center px-4 py-3 text-sm hover:bg-destructive/5 hover:text-destructive transition-colors"
                          >
                            <LogOut className="h-4 w-4 mr-3" />
                            Sign Out
                          </button>
                        </motion.div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </>
          ) : (
            <div className="flex items-center space-x-3">
              <MagneticElement>
                <Button
                  variant="ghost"
                  onClick={() => router.push(ROUTES.LOGIN)}
                  className="group"
                >
                  Sign In
                </Button>
              </MagneticElement>
              <MagneticElement>
                <Button
                  onClick={() => router.push(ROUTES.REGISTER)}
                  gradient
                  glow
                  className="group"
                >
                  Get Started
                  <Sparkles className="h-4 w-4 ml-2 group-hover:animate-pulse" />
                </Button>
              </MagneticElement>
            </div>
          )}
        </div>
      </div>

      {/* Mobile Navigation */}
      {isAuthenticated && (
        <div className="sm:hidden border-t border-border bg-background">
          <div className="container mx-auto px-4 py-2">
            <Button
              onClick={() => router.push(ROUTES.CV_CREATE)}
              className="w-full"
              size="sm"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create New CV
            </Button>
          </div>
        </div>
      )}
    </header>
  );
};
