'use client';

import React from 'react';
import { Header } from './Header';
import { Footer } from './Footer';
import { ToastContainer, useToast } from '@/components/ui/ToastNotification';

export interface MainLayoutProps {
  children: React.ReactNode;
  showFooter?: boolean;
  className?: string;
}

export const MainLayout: React.FC<MainLayoutProps> = ({
  children,
  showFooter = true,
  className,
}) => {
  const { toasts, removeToast } = useToast();

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className={`flex-1 ${className || ''}`}>
        {children}
      </main>
      
      {showFooter && <Footer />}
      
      {/* Toast notifications */}
      <ToastContainer toasts={toasts} onClose={removeToast} />
    </div>
  );
};
