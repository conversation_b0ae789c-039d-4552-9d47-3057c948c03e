'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Header } from './Header';
import { Footer } from './Footer';
import { ToastContainer, useToast } from '@/components/ui/ToastNotification';
import { pageVariants } from '@/lib/animations';

export interface MainLayoutProps {
  children: React.ReactNode;
  showFooter?: boolean;
  className?: string;
}

export const MainLayout: React.FC<MainLayoutProps> = ({
  children,
  showFooter = true,
  className,
}) => {
  const { toasts, removeToast } = useToast();

  return (
    <div className="min-h-screen flex flex-col bg-background text-foreground">
      <Header />

      <AnimatePresence mode="wait">
        <motion.main
          className={`flex-1 ${className || ''}`}
          variants={pageVariants}
          initial="initial"
          animate="animate"
          exit="exit"
        >
          {children}
        </motion.main>
      </AnimatePresence>

      {showFooter && <Footer />}

      {/* Toast notifications */}
      <ToastContainer toasts={toasts} onClose={removeToast} />
    </div>
  );
};
