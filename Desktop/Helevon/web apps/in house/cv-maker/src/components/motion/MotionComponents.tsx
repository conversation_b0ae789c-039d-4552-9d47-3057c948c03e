'use client';

import React from 'react';
import { motion, HTMLMotionProps, SVGMotionProps } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { 
  fadeInUp, 
  fadeInScale, 
  slideInLeft, 
  slideInRight, 
  staggerContainer,
  textReveal,
  staggeredText,
  letterAnimation
} from '@/lib/animations';

// Enhanced motion div with intersection observer
interface MotionDivProps extends HTMLMotionProps<'div'> {
  children: React.ReactNode;
  delay?: number;
  duration?: number;
  triggerOnce?: boolean;
  threshold?: number;
}

export const MotionDiv: React.FC<MotionDivProps> = ({
  children,
  delay = 0,
  duration = 0.6,
  triggerOnce = true,
  threshold = 0.1,
  ...props
}) => {
  const [ref, inView] = useInView({
    triggerOnce,
    threshold,
  });

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 40 }}
      animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 40 }}
      transition={{
        duration,
        delay,
        ease: [0.4, 0, 0.2, 1],
      }}
      {...props}
    >
      {children}
    </motion.div>
  );
};

// Fade in up animation
export const FadeInUp: React.FC<MotionDivProps> = ({ children, ...props }) => {
  const [ref, inView] = useInView({
    triggerOnce: props.triggerOnce ?? true,
    threshold: props.threshold ?? 0.1,
  });

  return (
    <motion.div
      ref={ref}
      variants={fadeInUp}
      initial="initial"
      animate={inView ? "animate" : "initial"}
      {...props}
    >
      {children}
    </motion.div>
  );
};

// Fade in with scale
export const FadeInScale: React.FC<MotionDivProps> = ({ children, ...props }) => {
  const [ref, inView] = useInView({
    triggerOnce: props.triggerOnce ?? true,
    threshold: props.threshold ?? 0.1,
  });

  return (
    <motion.div
      ref={ref}
      variants={fadeInScale}
      initial="initial"
      animate={inView ? "animate" : "initial"}
      {...props}
    >
      {children}
    </motion.div>
  );
};

// Slide in from left
export const SlideInLeft: React.FC<MotionDivProps> = ({ children, ...props }) => {
  const [ref, inView] = useInView({
    triggerOnce: props.triggerOnce ?? true,
    threshold: props.threshold ?? 0.1,
  });

  return (
    <motion.div
      ref={ref}
      variants={slideInLeft}
      initial="initial"
      animate={inView ? "animate" : "initial"}
      {...props}
    >
      {children}
    </motion.div>
  );
};

// Slide in from right
export const SlideInRight: React.FC<MotionDivProps> = ({ children, ...props }) => {
  const [ref, inView] = useInView({
    triggerOnce: props.triggerOnce ?? true,
    threshold: props.threshold ?? 0.1,
  });

  return (
    <motion.div
      ref={ref}
      variants={slideInRight}
      initial="initial"
      animate={inView ? "animate" : "initial"}
      {...props}
    >
      {children}
    </motion.div>
  );
};

// Stagger container
export const StaggerContainer: React.FC<MotionDivProps> = ({ children, ...props }) => {
  const [ref, inView] = useInView({
    triggerOnce: props.triggerOnce ?? true,
    threshold: props.threshold ?? 0.1,
  });

  return (
    <motion.div
      ref={ref}
      variants={staggerContainer}
      initial="initial"
      animate={inView ? "animate" : "initial"}
      {...props}
    >
      {children}
    </motion.div>
  );
};

// Animated text reveal
interface AnimatedTextProps {
  text: string;
  className?: string;
  delay?: number;
  stagger?: boolean;
}

export const AnimatedText: React.FC<AnimatedTextProps> = ({
  text,
  className = '',
  delay = 0,
  stagger = false,
}) => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  if (stagger) {
    return (
      <motion.div
        ref={ref}
        variants={staggeredText}
        initial="initial"
        animate={inView ? "animate" : "initial"}
        className={className}
      >
        {text.split('').map((char, index) => (
          <motion.span
            key={index}
            variants={letterAnimation}
            style={{ display: 'inline-block' }}
          >
            {char === ' ' ? '\u00A0' : char}
          </motion.span>
        ))}
      </motion.div>
    );
  }

  return (
    <motion.div
      ref={ref}
      variants={textReveal}
      initial="initial"
      animate={inView ? "animate" : "initial"}
      transition={{ delay }}
      className={className}
    >
      {text}
    </motion.div>
  );
};

// Floating element
interface FloatingElementProps extends HTMLMotionProps<'div'> {
  children: React.ReactNode;
  intensity?: number;
  duration?: number;
}

export const FloatingElement: React.FC<FloatingElementProps> = ({
  children,
  intensity = 10,
  duration = 4,
  ...props
}) => {
  return (
    <motion.div
      animate={{
        y: [-intensity, intensity, -intensity],
      }}
      transition={{
        duration,
        ease: 'easeInOut',
        repeat: Infinity,
      }}
      {...props}
    >
      {children}
    </motion.div>
  );
};

// Pulsing element
interface PulsingElementProps extends HTMLMotionProps<'div'> {
  children: React.ReactNode;
  scale?: number;
  duration?: number;
}

export const PulsingElement: React.FC<PulsingElementProps> = ({
  children,
  scale = 1.05,
  duration = 2,
  ...props
}) => {
  return (
    <motion.div
      animate={{
        scale: [1, scale, 1],
      }}
      transition={{
        duration,
        ease: 'easeInOut',
        repeat: Infinity,
      }}
      {...props}
    >
      {children}
    </motion.div>
  );
};

// Magnetic hover effect
interface MagneticElementProps extends HTMLMotionProps<'div'> {
  children: React.ReactNode;
  strength?: number;
}

export const MagneticElement: React.FC<MagneticElementProps> = ({
  children,
  strength = 20,
  ...props
}) => {
  const [mousePosition, setMousePosition] = React.useState({ x: 0, y: 0 });
  const [isHovered, setIsHovered] = React.useState(false);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    const deltaX = (e.clientX - centerX) / rect.width * strength;
    const deltaY = (e.clientY - centerY) / rect.height * strength;
    
    setMousePosition({ x: deltaX, y: deltaY });
  };

  return (
    <motion.div
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => {
        setIsHovered(false);
        setMousePosition({ x: 0, y: 0 });
      }}
      animate={{
        x: isHovered ? mousePosition.x : 0,
        y: isHovered ? mousePosition.y : 0,
      }}
      transition={{
        type: 'spring',
        stiffness: 300,
        damping: 30,
      }}
      {...props}
    >
      {children}
    </motion.div>
  );
};

// Parallax element
interface ParallaxElementProps extends HTMLMotionProps<'div'> {
  children: React.ReactNode;
  speed?: number;
}

export const ParallaxElement: React.FC<ParallaxElementProps> = ({
  children,
  speed = 0.5,
  ...props
}) => {
  const [scrollY, setScrollY] = React.useState(0);

  React.useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <motion.div
      style={{
        y: scrollY * speed,
      }}
      {...props}
    >
      {children}
    </motion.div>
  );
};
