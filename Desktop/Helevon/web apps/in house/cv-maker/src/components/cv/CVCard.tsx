'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { Edit, Eye, Copy, Trash2, MoreVertical, Calendar, Globe } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { ConfirmModal } from '@/components/ui/Modal';
import { useToast } from '@/components/ui/ToastNotification';
import { useCVStore } from '@/stores/cv';
import { formatDate } from '@/lib/utils';
import { SUPPORTED_LANGUAGES, CV_TEMPLATES } from '@/lib/constants';
import type { CVListItem } from '@/types/cv';

interface CVCardProps {
  cv: CVListItem;
}

export const CVCard: React.FC<CVCardProps> = ({ cv }) => {
  const { deleteCV, isLoading } = useCVStore();
  const { toast } = useToast();
  const [showActions, setShowActions] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const handleDelete = async () => {
    try {
      await deleteCV(cv.id);
      toast.success('CV deleted successfully');
      setShowDeleteModal(false);
    } catch (error: any) {
      toast.error('Failed to delete CV', error.message);
    }
  };

  const handleDuplicate = async () => {
    try {
      // TODO: Implement duplicate functionality
      toast.info('Duplicate feature coming soon');
    } catch (error: any) {
      toast.error('Failed to duplicate CV', error.message);
    }
  };

  return (
    <>
      <div className="bg-background border border-border rounded-xl p-6 hover:shadow-md transition-shadow group">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold truncate group-hover:text-primary transition-colors">
              {cv.title}
            </h3>
            <div className="flex items-center gap-4 mt-2 text-sm text-muted">
              <div className="flex items-center gap-1">
                <Globe className="h-4 w-4" />
                <span>{SUPPORTED_LANGUAGES[cv.language]}</span>
              </div>
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span>Updated {formatDate(cv.updated_at)}</span>
              </div>
            </div>
          </div>

          {/* Actions Menu */}
          <div className="relative">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowActions(!showActions)}
              className="opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <MoreVertical className="h-4 w-4" />
            </Button>

            {showActions && (
              <div className="absolute right-0 mt-2 w-48 bg-background border border-border rounded-md shadow-lg z-10">
                <div className="py-1">
                  <Link
                    href={`/cv/${cv.id}/edit`}
                    className="flex items-center px-4 py-2 text-sm hover:bg-accent hover:text-accent-foreground"
                    onClick={() => setShowActions(false)}
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </Link>
                  
                  <Link
                    href={`/cv/${cv.id}/preview`}
                    className="flex items-center px-4 py-2 text-sm hover:bg-accent hover:text-accent-foreground"
                    onClick={() => setShowActions(false)}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Preview & Export
                  </Link>
                  
                  <button
                    onClick={() => {
                      setShowActions(false);
                      handleDuplicate();
                    }}
                    className="flex w-full items-center px-4 py-2 text-sm hover:bg-accent hover:text-accent-foreground"
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Duplicate
                  </button>
                  
                  <div className="border-t border-border my-1" />
                  
                  <button
                    onClick={() => {
                      setShowActions(false);
                      setShowDeleteModal(true);
                    }}
                    className="flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Template Info */}
        <div className="mb-4">
          <div className="inline-flex items-center px-2 py-1 bg-primary/10 text-primary text-xs font-medium rounded">
            {CV_TEMPLATES[cv.template]} Template
          </div>
        </div>

        {/* Preview Area */}
        <div className="bg-secondary/30 rounded-lg p-4 mb-4 min-h-[120px] flex items-center justify-center">
          <div className="text-center text-muted">
            <Eye className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">CV Preview</p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2">
          <Button
            asChild
            href={`/cv/${cv.id}/edit`}
            className="flex-1"
            size="sm"
          >
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>

          <Button
            asChild
            href={`/cv/${cv.id}/preview`}
            variant="outline"
            className="flex-1"
            size="sm"
          >
            <Eye className="h-4 w-4 mr-2" />
            Preview
          </Button>
        </div>

        {/* Created Date */}
        <div className="mt-4 pt-4 border-t border-border">
          <p className="text-xs text-muted">
            Created {formatDate(cv.created_at)}
          </p>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      <ConfirmModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDelete}
        title="Delete CV"
        message={`Are you sure you want to delete "${cv.title}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        variant="danger"
        isLoading={isLoading}
      />

      {/* Click outside to close actions menu */}
      {showActions && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setShowActions(false)}
        />
      )}
    </>
  );
};
