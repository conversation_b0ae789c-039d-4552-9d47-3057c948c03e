'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FileText, Upload, X, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { FadeInUp, MagneticElement } from '@/components/motion/MotionComponents';
import { useToast } from '@/components/ui/ToastNotification';

interface CoverLetter {
  recipientName: string;
  recipientTitle: string;
  company: string;
  address: string;
  subject: string;
  content: string;
  signature?: string;
}

interface CoverLetterSectionProps {
  cvId: string | null;
  onComplete: () => void;
  isCompleted: boolean;
}

export const CoverLetterSection: React.FC<CoverLetterSectionProps> = ({
  cvId,
  onComplete,
  isCompleted
}) => {
  const { toast } = useToast();
  const [includeCoverLetter, setIncludeCoverLetter] = useState(false);
  const [coverLetter, setCoverLetter] = useState<CoverLetter>({
    recipientName: '',
    recipientTitle: '',
    company: '',
    address: '',
    subject: '',
    content: '',
    signature: ''
  });
  const [isUploading, setIsUploading] = useState(false);

  const updateCoverLetter = (field: keyof CoverLetter, value: string) => {
    setCoverLetter(prev => ({ ...prev, [field]: value }));
  };

  const handleSignatureUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      toast.error('Invalid file type', 'Please upload an image file');
      return;
    }

    if (file.size > 2 * 1024 * 1024) {
      toast.error('File too large', 'Please upload an image smaller than 2MB');
      return;
    }

    setIsUploading(true);
    try {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setCoverLetter(prev => ({ ...prev, signature: result }));
        setIsUploading(false);
        toast.success('Signature uploaded successfully');
      };
      reader.readAsDataURL(file);
    } catch (error) {
      setIsUploading(false);
      toast.error('Upload failed', 'Please try again');
    }
  };

  const handleRemoveSignature = () => {
    setCoverLetter(prev => ({ ...prev, signature: '' }));
  };

  const handleSave = () => {
    if (includeCoverLetter) {
      if (!coverLetter.company.trim() || !coverLetter.subject.trim() || !coverLetter.content.trim()) {
        toast.error('Please fill in required fields', 'Company, subject, and content are required');
        return;
      }
    }

    onComplete();
    toast.success('Cover letter saved!');
  };

  const defaultContent = `Dear Hiring Manager,

I am writing to express my strong interest in the [Position Title] role at [Company Name]. With my background in [Your Field/Industry] and passion for [Relevant Area], I am excited about the opportunity to contribute to your team.

In my previous role at [Previous Company], I successfully [Key Achievement 1]. Additionally, I [Key Achievement 2], which demonstrates my ability to [Relevant Skill]. These experiences have prepared me well for the challenges and opportunities at [Company Name].

I am particularly drawn to [Company Name] because of [Specific Reason - research the company]. I believe my skills in [Relevant Skills] and my commitment to [Relevant Value] make me an ideal candidate for this position.

I would welcome the opportunity to discuss how my experience and enthusiasm can contribute to your team's success. Thank you for considering my application.

Sincerely,
[Your Name]`;

  return (
    <div className="space-y-8">
      <FadeInUp>
        <div className="text-center mb-8">
          <h3 className="text-lg font-semibold mb-2">Cover Letter</h3>
          <p className="text-foreground-muted">
            Create a personalized cover letter to accompany your CV
          </p>
        </div>
      </FadeInUp>

      {/* Include Cover Letter Toggle */}
      <FadeInUp>
        <div className="bg-background-elevated rounded-2xl p-6 border border-border">
          <div className="flex items-center gap-3">
            <input
              type="checkbox"
              id="include-cover-letter"
              checked={includeCoverLetter}
              onChange={(e) => setIncludeCoverLetter(e.target.checked)}
              className="h-4 w-4 text-primary focus:ring-primary border-border rounded"
            />
            <label htmlFor="include-cover-letter" className="text-sm font-medium">
              Include a cover letter with my CV
            </label>
          </div>
          <p className="text-xs text-foreground-muted mt-2 ml-7">
            A well-written cover letter can significantly improve your chances
          </p>
        </div>
      </FadeInUp>

      {includeCoverLetter && (
        <>
          {/* Recipient Information */}
          <FadeInUp>
            <div className="bg-background-elevated rounded-2xl p-6 border border-border">
              <h4 className="text-lg font-medium mb-4 flex items-center gap-2">
                <FileText className="h-5 w-5 text-primary" />
                Recipient Information
              </h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Recipient Name (Optional)"
                  value={coverLetter.recipientName}
                  onChange={(e) => updateCoverLetter('recipientName', e.target.value)}
                  placeholder="Hiring Manager or specific name"
                />

                <Input
                  label="Recipient Title (Optional)"
                  value={coverLetter.recipientTitle}
                  onChange={(e) => updateCoverLetter('recipientTitle', e.target.value)}
                  placeholder="HR Manager, Director, etc."
                />

                <Input
                  label="Company Name"
                  value={coverLetter.company}
                  onChange={(e) => updateCoverLetter('company', e.target.value)}
                  placeholder="Company you're applying to"
                  required
                />

                <Input
                  label="Company Address (Optional)"
                  value={coverLetter.address}
                  onChange={(e) => updateCoverLetter('address', e.target.value)}
                  placeholder="Company address"
                />
              </div>
            </div>
          </FadeInUp>

          {/* Cover Letter Content */}
          <FadeInUp>
            <div className="bg-background-elevated rounded-2xl p-6 border border-border">
              <h4 className="text-lg font-medium mb-4">Cover Letter Content</h4>
              
              <div className="space-y-4">
                <Input
                  label="Subject Line"
                  value={coverLetter.subject}
                  onChange={(e) => updateCoverLetter('subject', e.target.value)}
                  placeholder="Application for [Position Title] - [Your Name]"
                  required
                />

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Letter Content <span className="text-destructive">*</span>
                  </label>
                  <textarea
                    value={coverLetter.content}
                    onChange={(e) => updateCoverLetter('content', e.target.value)}
                    placeholder={defaultContent}
                    className="w-full p-4 border border-border rounded-lg bg-background focus:border-primary focus:ring-2 focus:ring-primary/20 transition-colors resize-none"
                    rows={15}
                  />
                  <p className="text-xs text-foreground-muted mt-2">
                    Customize the template above or write your own cover letter
                  </p>
                </div>
              </div>
            </div>
          </FadeInUp>

          {/* Signature Upload */}
          <FadeInUp>
            <div className="bg-background-elevated rounded-2xl p-6 border border-border">
              <h4 className="text-lg font-medium mb-4">Digital Signature (Optional)</h4>
              
              <div className="flex items-center gap-6">
                {coverLetter.signature ? (
                  <div className="relative group">
                    <img
                      src={coverLetter.signature}
                      alt="Signature"
                      className="h-16 border border-border rounded-lg bg-white p-2"
                    />
                    <button
                      onClick={handleRemoveSignature}
                      className="absolute -top-2 -right-2 bg-destructive text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                ) : (
                  <label
                    htmlFor="signature-upload"
                    className="flex items-center gap-2 px-4 py-2 border border-dashed border-border rounded-lg cursor-pointer hover:border-primary transition-colors"
                  >
                    {isUploading ? (
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      >
                        <Upload className="h-4 w-4 text-primary" />
                      </motion.div>
                    ) : (
                      <Upload className="h-4 w-4" />
                    )}
                    <span className="text-sm">Upload Signature</span>
                  </label>
                )}
                
                <input
                  id="signature-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleSignatureUpload}
                  className="hidden"
                  disabled={isUploading}
                />
                
                <p className="text-xs text-foreground-muted">
                  Upload an image of your signature (PNG, JPG, max 2MB)
                </p>
              </div>
            </div>
          </FadeInUp>
        </>
      )}

      <FadeInUp className="flex justify-end pt-6 border-t border-border">
        <MagneticElement>
          <Button
            onClick={handleSave}
            size="lg"
            gradient={!isCompleted}
            variant={isCompleted ? "success" : "primary"}
            className="group"
          >
            {isCompleted ? (
              <>
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ type: "spring", stiffness: 500, damping: 30 }}
                >
                  <FileText className="h-5 w-5 mr-2" />
                </motion.div>
                Cover Letter Saved
              </>
            ) : (
              <>
                Save & Continue
                <motion.div
                  className="ml-2"
                  animate={{ x: [0, 4, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                >
                  <FileText className="h-5 w-5" />
                </motion.div>
              </>
            )}
          </Button>
        </MagneticElement>
      </FadeInUp>
    </div>
  );
};
