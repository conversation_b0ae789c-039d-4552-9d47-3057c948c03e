'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Award, Plus, X, Star } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { FadeInUp, MagneticElement } from '@/components/motion/MotionComponents';
import { useToast } from '@/components/ui/ToastNotification';

interface Skill {
  id: string;
  name: string;
  level: number; // 1-5
  category: 'technical' | 'soft' | 'language';
}

interface SkillsSectionProps {
  cvId: string | null;
  onComplete: () => void;
  isCompleted: boolean;
}

export const SkillsSection: React.FC<SkillsSectionProps> = ({
  cvId,
  onComplete,
  isCompleted
}) => {
  const { toast } = useToast();
  const [skills, setSkills] = useState<Skill[]>([]);
  const [newSkill, setNewSkill] = useState({ name: '', category: 'technical' as const });

  const addSkill = () => {
    if (!newSkill.name.trim()) {
      toast.error('Please enter a skill name');
      return;
    }

    const skill: Skill = {
      id: Date.now().toString(),
      name: newSkill.name.trim(),
      level: 3,
      category: newSkill.category
    };

    setSkills([...skills, skill]);
    setNewSkill({ name: '', category: 'technical' });
  };

  const removeSkill = (id: string) => {
    setSkills(prev => prev.filter(skill => skill.id !== id));
  };

  const updateSkillLevel = (id: string, level: number) => {
    setSkills(prev =>
      prev.map(skill => (skill.id === id ? { ...skill, level } : skill))
    );
  };

  const handleSave = () => {
    if (skills.length === 0) {
      toast.error('Please add at least one skill');
      return;
    }

    onComplete();
    toast.success('Skills saved!');
  };

  const skillsByCategory = {
    technical: skills.filter(s => s.category === 'technical'),
    soft: skills.filter(s => s.category === 'soft'),
    language: skills.filter(s => s.category === 'language')
  };

  const renderStars = (skillId: string, currentLevel: number) => {
    return (
      <div className="flex gap-1">
        {[1, 2, 3, 4, 5].map(level => (
          <motion.button
            key={level}
            onClick={() => updateSkillLevel(skillId, level)}
            className={`w-5 h-5 ${
              level <= currentLevel ? 'text-warning' : 'text-border'
            } hover:text-warning transition-colors`}
            whileHover={{ scale: 1.2 }}
            whileTap={{ scale: 0.9 }}
          >
            <Star className={`w-full h-full ${level <= currentLevel ? 'fill-current' : ''}`} />
          </motion.button>
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-8">
      <FadeInUp>
        <div className="text-center mb-8">
          <h3 className="text-lg font-semibold mb-2">Skills & Expertise</h3>
          <p className="text-foreground-muted">
            Highlight your technical skills, soft skills, and languages
          </p>
        </div>
      </FadeInUp>

      {/* Add New Skill */}
      <FadeInUp>
        <div className="bg-background-elevated rounded-2xl p-6 border border-border">
          <h4 className="text-lg font-medium mb-4 flex items-center gap-2">
            <Plus className="h-5 w-5 text-primary" />
            Add New Skill
          </h4>
          
          <div className="flex gap-4">
            <Input
              value={newSkill.name}
              onChange={(e) => setNewSkill(prev => ({ ...prev, name: e.target.value }))}
              placeholder="Enter skill name"
              className="flex-1"
              onKeyPress={(e) => e.key === 'Enter' && addSkill()}
            />
            
            <select
              value={newSkill.category}
              onChange={(e) => setNewSkill(prev => ({ ...prev, category: e.target.value as any }))}
              className="px-3 py-2 border border-border rounded-lg bg-background focus:border-primary focus:ring-2 focus:ring-primary/20 transition-colors"
            >
              <option value="technical">Technical</option>
              <option value="soft">Soft Skills</option>
              <option value="language">Language</option>
            </select>
            
            <Button onClick={addSkill}>Add</Button>
          </div>
        </div>
      </FadeInUp>

      {/* Skills by Category */}
      {Object.entries(skillsByCategory).map(([category, categorySkills]) => (
        categorySkills.length > 0 && (
          <FadeInUp key={category}>
            <div className="bg-background-elevated rounded-2xl p-6 border border-border">
              <h4 className="text-lg font-medium mb-4 capitalize flex items-center gap-2">
                <Award className="h-5 w-5 text-primary" />
                {category === 'technical' ? 'Technical Skills' : 
                 category === 'soft' ? 'Soft Skills' : 'Languages'}
              </h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {categorySkills.map(skill => (
                  <motion.div
                    key={skill.id}
                    className="flex items-center justify-between p-3 bg-secondary/30 rounded-lg"
                    whileHover={{ scale: 1.02 }}
                  >
                    <div className="flex-1">
                      <span className="font-medium">{skill.name}</span>
                      <div className="mt-1">
                        {renderStars(skill.id, skill.level)}
                      </div>
                    </div>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeSkill(skill.id)}
                      className="text-destructive hover:text-destructive ml-2"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </motion.div>
                ))}
              </div>
            </div>
          </FadeInUp>
        )
      ))}

      {skills.length === 0 && (
        <FadeInUp>
          <div className="text-center py-12 text-foreground-muted">
            <Award className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No skills added yet. Add your first skill above!</p>
          </div>
        </FadeInUp>
      )}

      <FadeInUp className="flex justify-end pt-6 border-t border-border">
        <MagneticElement>
          <Button
            onClick={handleSave}
            size="lg"
            gradient={!isCompleted}
            variant={isCompleted ? "success" : "primary"}
            className="group"
          >
            {isCompleted ? (
              <>
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ type: "spring", stiffness: 500, damping: 30 }}
                >
                  <Award className="h-5 w-5 mr-2" />
                </motion.div>
                Skills Saved
              </>
            ) : (
              <>
                Save & Continue
                <motion.div
                  className="ml-2"
                  animate={{ x: [0, 4, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                >
                  <Award className="h-5 w-5" />
                </motion.div>
              </>
            )}
          </Button>
        </MagneticElement>
      </FadeInUp>
    </div>
  );
};
