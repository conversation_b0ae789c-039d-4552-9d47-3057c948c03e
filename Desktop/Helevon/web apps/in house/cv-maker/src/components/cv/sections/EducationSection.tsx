'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { GraduationCap, Plus, Calendar, MapPin, Award } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { FadeInUp, MagneticElement } from '@/components/motion/MotionComponents';
import { useToast } from '@/components/ui/ToastNotification';

interface Education {
  id: string;
  institution: string;
  degree: string;
  field: string;
  startDate: string;
  endDate: string;
  location: string;
  gpa?: string;
  description?: string;
}

interface EducationSectionProps {
  cvId: string | null;
  onComplete: () => void;
  isCompleted: boolean;
}

export const EducationSection: React.FC<EducationSectionProps> = ({
  cvId,
  onComplete,
  isCompleted
}) => {
  const { toast } = useToast();
  const [educationList, setEducationList] = useState<Education[]>([
    {
      id: '1',
      institution: '',
      degree: '',
      field: '',
      startDate: '',
      endDate: '',
      location: '',
      gpa: '',
      description: ''
    }
  ]);

  const addEducation = () => {
    const newEducation: Education = {
      id: Date.now().toString(),
      institution: '',
      degree: '',
      field: '',
      startDate: '',
      endDate: '',
      location: '',
      gpa: '',
      description: ''
    };
    setEducationList([...educationList, newEducation]);
  };

  const updateEducation = (id: string, field: keyof Education, value: string) => {
    setEducationList(prev =>
      prev.map(edu => (edu.id === id ? { ...edu, [field]: value } : edu))
    );
  };

  const removeEducation = (id: string) => {
    if (educationList.length > 1) {
      setEducationList(prev => prev.filter(edu => edu.id !== id));
    }
  };

  const handleSave = () => {
    // Validate at least one education entry has required fields
    const hasValidEntry = educationList.some(edu => 
      edu.institution.trim() && edu.degree.trim() && edu.field.trim()
    );

    if (!hasValidEntry) {
      toast.error('Please fill in at least one education entry', 'Institution, degree, and field are required');
      return;
    }

    onComplete();
    toast.success('Education information saved!');
  };

  return (
    <div className="space-y-8">
      <FadeInUp>
        <div className="text-center mb-8">
          <h3 className="text-lg font-semibold mb-2">Educational Background</h3>
          <p className="text-foreground-muted">
            Add your academic qualifications and achievements
          </p>
        </div>
      </FadeInUp>

      <div className="space-y-6">
        {educationList.map((education, index) => (
          <FadeInUp key={education.id} delay={index * 0.1}>
            <motion.div
              className="bg-background-elevated rounded-2xl p-6 border border-border"
              whileHover={{ y: -2 }}
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
            >
              <div className="flex justify-between items-center mb-6">
                <h4 className="text-lg font-medium flex items-center gap-2">
                  <GraduationCap className="h-5 w-5 text-primary" />
                  Education {index + 1}
                </h4>
                {educationList.length > 1 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeEducation(education.id)}
                    className="text-destructive hover:text-destructive"
                  >
                    Remove
                  </Button>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Institution"
                  value={education.institution}
                  onChange={(e) => updateEducation(education.id, 'institution', e.target.value)}
                  placeholder="University name"
                  required
                />

                <Input
                  label="Degree"
                  value={education.degree}
                  onChange={(e) => updateEducation(education.id, 'degree', e.target.value)}
                  placeholder="Bachelor's, Master's, PhD, etc."
                  required
                />

                <Input
                  label="Field of Study"
                  value={education.field}
                  onChange={(e) => updateEducation(education.id, 'field', e.target.value)}
                  placeholder="Computer Science, Business, etc."
                  required
                />

                <Input
                  label="Location"
                  value={education.location}
                  onChange={(e) => updateEducation(education.id, 'location', e.target.value)}
                  placeholder="City, Country"
                  icon={<MapPin className="h-4 w-4" />}
                />

                <Input
                  label="Start Date"
                  type="month"
                  value={education.startDate}
                  onChange={(e) => updateEducation(education.id, 'startDate', e.target.value)}
                  icon={<Calendar className="h-4 w-4" />}
                />

                <Input
                  label="End Date"
                  type="month"
                  value={education.endDate}
                  onChange={(e) => updateEducation(education.id, 'endDate', e.target.value)}
                  placeholder="Leave empty if current"
                  icon={<Calendar className="h-4 w-4" />}
                />

                <Input
                  label="GPA (Optional)"
                  value={education.gpa}
                  onChange={(e) => updateEducation(education.id, 'gpa', e.target.value)}
                  placeholder="3.8/4.0"
                  icon={<Award className="h-4 w-4" />}
                />
              </div>

              <div className="mt-4">
                <label className="block text-sm font-medium mb-2">
                  Description (Optional)
                </label>
                <textarea
                  value={education.description}
                  onChange={(e) => updateEducation(education.id, 'description', e.target.value)}
                  placeholder="Relevant coursework, achievements, honors..."
                  className="w-full p-3 border border-border rounded-lg bg-background focus:border-primary focus:ring-2 focus:ring-primary/20 transition-colors resize-none"
                  rows={3}
                />
              </div>
            </motion.div>
          </FadeInUp>
        ))}
      </div>

      <FadeInUp>
        <div className="flex justify-center">
          <MagneticElement>
            <Button
              onClick={addEducation}
              variant="outline"
              className="group"
            >
              <Plus className="h-4 w-4 mr-2 group-hover:rotate-90 transition-transform" />
              Add Another Education
            </Button>
          </MagneticElement>
        </div>
      </FadeInUp>

      <FadeInUp className="flex justify-end pt-6 border-t border-border">
        <MagneticElement>
          <Button
            onClick={handleSave}
            size="lg"
            gradient={!isCompleted}
            variant={isCompleted ? "success" : "primary"}
            className="group"
          >
            {isCompleted ? (
              <>
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ type: "spring", stiffness: 500, damping: 30 }}
                >
                  <GraduationCap className="h-5 w-5 mr-2" />
                </motion.div>
                Education Saved
              </>
            ) : (
              <>
                Save & Continue
                <motion.div
                  className="ml-2"
                  animate={{ x: [0, 4, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                >
                  <GraduationCap className="h-5 w-5" />
                </motion.div>
              </>
            )}
          </Button>
        </MagneticElement>
      </FadeInUp>
    </div>
  );
};
