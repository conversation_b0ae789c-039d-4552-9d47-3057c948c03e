'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { GraduationCap, Plus, Calendar, MapPin, Award, Upload, X, FileText } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { FadeInUp, MagneticElement } from '@/components/motion/MotionComponents';
import { useToast } from '@/components/ui/ToastNotification';
import { useCVStore } from '@/stores/cv';

interface Education {
  id: string;
  institution: string;
  degree: string;
  field: string;
  startDate: string;
  endDate: string;
  location: string;
  gpa?: string;
  description?: string;
  certificates?: any[]; // Files uploaded for this education entry
}

interface EducationSectionProps {
  cvId: string | null;
  onComplete: () => void;
  isCompleted: boolean;
}

export const EducationSection: React.FC<EducationSectionProps> = ({
  cvId,
  onComplete,
  isCompleted
}) => {
  const { toast } = useToast();
  const { currentCV } = useCVStore();
  const [educationList, setEducationList] = useState<Education[]>([
    {
      id: '1',
      institution: '',
      degree: '',
      field: '',
      startDate: '',
      endDate: '',
      location: '',
      gpa: '',
      description: '',
      certificates: []
    }
  ]);
  const [uploadingFiles, setUploadingFiles] = useState<Set<string>>(new Set());

  // Populate education data from CV when available
  useEffect(() => {
    if (currentCV?.education && currentCV.education.length > 0) {
      setEducationList(currentCV.education.map((edu: any) => ({
        id: edu.id || Date.now().toString(),
        institution: edu.institution || '',
        degree: edu.degree || '',
        field: edu.fieldOfStudy || edu.field || '',
        startDate: edu.startDate || '',
        endDate: edu.endDate || '',
        location: edu.location || '',
        gpa: edu.grade || edu.gpa || '',
        description: edu.description || '',
        certificates: edu.certificates || []
      })));
    }
  }, [currentCV]);

  const addEducation = () => {
    const newEducation: Education = {
      id: Date.now().toString(),
      institution: '',
      degree: '',
      field: '',
      startDate: '',
      endDate: '',
      location: '',
      gpa: '',
      description: '',
      certificates: []
    };
    setEducationList([...educationList, newEducation]);
  };

  const handleCertificateUpload = async (educationId: string, event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type (PDF, images)
    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg'];
    if (!allowedTypes.includes(file.type)) {
      toast.error('Invalid file type', 'Please upload PDF, JPG, or PNG files');
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast.error('File too large', 'Please upload a file smaller than 10MB');
      return;
    }

    if (!cvId) {
      toast.error('CV not found', 'Please try refreshing the page');
      return;
    }

    setUploadingFiles(prev => new Set([...prev, educationId]));
    try {
      const { uploadFile } = useCVStore.getState();
      const uploadedFile = await uploadFile(cvId, file, 'certificate');

      // Update education entry with new certificate
      setEducationList(prev =>
        prev.map(edu =>
          edu.id === educationId
            ? { ...edu, certificates: [...(edu.certificates || []), uploadedFile] }
            : edu
        )
      );

      toast.success('Certificate uploaded successfully');
    } catch (error: any) {
      toast.error('Upload failed', error.message || 'Please try again');
    } finally {
      setUploadingFiles(prev => {
        const newSet = new Set(prev);
        newSet.delete(educationId);
        return newSet;
      });
    }
  };

  const removeCertificate = async (educationId: string, certificateId: string) => {
    try {
      const { deleteFile } = useCVStore.getState();
      await deleteFile(certificateId);

      // Remove certificate from education entry
      setEducationList(prev =>
        prev.map(edu =>
          edu.id === educationId
            ? { ...edu, certificates: edu.certificates?.filter(cert => cert.id !== certificateId) || [] }
            : edu
        )
      );

      toast.success('Certificate removed successfully');
    } catch (error: any) {
      toast.error('Failed to remove certificate', error.message || 'Please try again');
    }
  };

  const updateEducation = (id: string, field: keyof Education, value: string) => {
    setEducationList(prev =>
      prev.map(edu => (edu.id === id ? { ...edu, [field]: value } : edu))
    );
  };

  const removeEducation = (id: string) => {
    if (educationList.length > 1) {
      setEducationList(prev => prev.filter(edu => edu.id !== id));
    }
  };

  const handleSave = async () => {
    // Validate at least one education entry has required fields
    const hasValidEntry = educationList.some(edu =>
      edu.institution.trim() && edu.degree.trim() && edu.field.trim()
    );

    if (!hasValidEntry) {
      toast.error('Please fill in at least one education entry', 'Institution, degree, and field are required');
      return;
    }

    if (!cvId) {
      toast.error('CV not found', 'Please try refreshing the page');
      return;
    }

    try {
      // Use the CV store to update education via API
      // This will call PUT /cvs/{cv_id}/education as per CVManagement.md
      const { updateEducation } = useCVStore.getState();
      const educationData = educationList.map(edu => ({
        id: edu.id,
        institution: edu.institution,
        degree: edu.degree,
        fieldOfStudy: edu.field,
        startDate: edu.startDate,
        endDate: edu.endDate,
        isCurrentlyStudying: !edu.endDate,
        grade: edu.gpa,
        description: edu.description,
        certificates: edu.certificates || []
      }));

      await updateEducation(cvId, { education: educationData });

      onComplete();
      toast.success('Education information saved!', 'Ready to move to the next section');
    } catch (error: any) {
      toast.error('Failed to save', error.message || 'Please try again');
    }
  };

  return (
    <div className="space-y-8">
      <FadeInUp>
        <div className="text-center mb-8">
          <h3 className="text-lg font-semibold mb-2">Educational Background</h3>
          <p className="text-foreground-muted">
            Add your academic qualifications and achievements
          </p>
        </div>
      </FadeInUp>

      <div className="space-y-6">
        {educationList.map((education, index) => (
          <FadeInUp key={education.id} delay={index * 0.1}>
            <motion.div
              className="bg-background-elevated rounded-2xl p-6 border border-border"
              whileHover={{ y: -2 }}
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
            >
              <div className="flex justify-between items-center mb-6">
                <h4 className="text-lg font-medium flex items-center gap-2">
                  <GraduationCap className="h-5 w-5 text-primary" />
                  Education {index + 1}
                </h4>
                {educationList.length > 1 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeEducation(education.id)}
                    className="text-destructive hover:text-destructive"
                  >
                    Remove
                  </Button>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Institution"
                  value={education.institution}
                  onChange={(e) => updateEducation(education.id, 'institution', e.target.value)}
                  placeholder="University name"
                  required
                />

                <Input
                  label="Degree"
                  value={education.degree}
                  onChange={(e) => updateEducation(education.id, 'degree', e.target.value)}
                  placeholder="Bachelor's, Master's, PhD, etc."
                  required
                />

                <Input
                  label="Field of Study"
                  value={education.field}
                  onChange={(e) => updateEducation(education.id, 'field', e.target.value)}
                  placeholder="Computer Science, Business, etc."
                  required
                />

                <Input
                  label="Location"
                  value={education.location}
                  onChange={(e) => updateEducation(education.id, 'location', e.target.value)}
                  placeholder="City, Country"
                  icon={<MapPin className="h-4 w-4" />}
                />

                <Input
                  label="Start Date"
                  type="month"
                  value={education.startDate}
                  onChange={(e) => updateEducation(education.id, 'startDate', e.target.value)}
                  icon={<Calendar className="h-4 w-4" />}
                />

                <Input
                  label="End Date"
                  type="month"
                  value={education.endDate}
                  onChange={(e) => updateEducation(education.id, 'endDate', e.target.value)}
                  placeholder="Leave empty if current"
                  icon={<Calendar className="h-4 w-4" />}
                />

                <Input
                  label="GPA (Optional)"
                  value={education.gpa}
                  onChange={(e) => updateEducation(education.id, 'gpa', e.target.value)}
                  placeholder="3.8/4.0"
                  icon={<Award className="h-4 w-4" />}
                />
              </div>

              <div className="mt-4">
                <label className="block text-sm font-medium mb-2">
                  Description (Optional)
                </label>
                <textarea
                  value={education.description}
                  onChange={(e) => updateEducation(education.id, 'description', e.target.value)}
                  placeholder="Relevant coursework, achievements, honors..."
                  className="w-full p-3 border border-border rounded-lg bg-background focus:border-primary focus:ring-2 focus:ring-primary/20 transition-colors resize-none"
                  rows={3}
                />
              </div>

              {/* Certificate Upload Section */}
              <div className="mt-6 p-4 bg-secondary/20 rounded-lg">
                <h5 className="text-sm font-medium mb-3 flex items-center gap-2">
                  <Award className="h-4 w-4 text-primary" />
                  Certificates & Documents
                </h5>

                {/* Existing Certificates */}
                {education.certificates && education.certificates.length > 0 && (
                  <div className="mb-4 space-y-2">
                    {education.certificates.map((cert: any) => (
                      <div key={cert.id} className="flex items-center justify-between p-2 bg-background rounded border">
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4 text-primary" />
                          <span className="text-sm font-medium">{cert.name}</span>
                          <span className="text-xs text-foreground-muted">
                            ({(cert.size / 1024 / 1024).toFixed(1)} MB)
                          </span>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeCertificate(education.id, cert.id)}
                          className="text-destructive hover:text-destructive"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}

                {/* Upload Button */}
                <label
                  htmlFor={`certificate-upload-${education.id}`}
                  className="flex items-center justify-center gap-2 p-3 border-2 border-dashed border-border hover:border-primary rounded-lg cursor-pointer transition-colors group"
                >
                  {uploadingFiles.has(education.id) ? (
                    <>
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      >
                        <Upload className="h-4 w-4 text-primary" />
                      </motion.div>
                      <span className="text-sm text-primary">Uploading...</span>
                    </>
                  ) : (
                    <>
                      <Upload className="h-4 w-4 text-foreground-muted group-hover:text-primary transition-colors" />
                      <span className="text-sm text-foreground-muted group-hover:text-primary transition-colors">
                        Upload Certificate
                      </span>
                    </>
                  )}
                </label>
                <input
                  id={`certificate-upload-${education.id}`}
                  type="file"
                  accept=".pdf,.jpg,.jpeg,.png"
                  onChange={(e) => handleCertificateUpload(education.id, e)}
                  className="hidden"
                  disabled={uploadingFiles.has(education.id)}
                />
                <p className="text-xs text-foreground-muted mt-2">
                  Upload certificates, diplomas, or transcripts (PDF, JPG, PNG - Max 10MB)
                </p>
              </div>
            </motion.div>
          </FadeInUp>
        ))}
      </div>

      <FadeInUp>
        <div className="flex justify-center">
          <MagneticElement>
            <Button
              onClick={addEducation}
              variant="outline"
              className="group"
            >
              <Plus className="h-4 w-4 mr-2 group-hover:rotate-90 transition-transform" />
              Add Another Education
            </Button>
          </MagneticElement>
        </div>
      </FadeInUp>

      <FadeInUp className="flex justify-end pt-6 border-t border-border">
        <MagneticElement>
          <Button
            onClick={handleSave}
            size="lg"
            gradient={!isCompleted}
            variant={isCompleted ? "success" : "primary"}
            className="group"
          >
            {isCompleted ? (
              <>
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ type: "spring", stiffness: 500, damping: 30 }}
                >
                  <GraduationCap className="h-5 w-5 mr-2" />
                </motion.div>
                Education Saved
              </>
            ) : (
              <>
                Save & Continue
                <motion.div
                  className="ml-2"
                  animate={{ x: [0, 4, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                >
                  <GraduationCap className="h-5 w-5" />
                </motion.div>
              </>
            )}
          </Button>
        </MagneticElement>
      </FadeInUp>
    </div>
  );
};
