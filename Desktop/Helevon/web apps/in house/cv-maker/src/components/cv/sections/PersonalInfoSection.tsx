'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { User, Mail, Phone, MapPin, Globe, Camera, Upload, X } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { FadeInUp, MagneticElement } from '@/components/motion/MotionComponents';
import { useToast } from '@/components/ui/ToastNotification';
import { useCVStore } from '@/stores/cv';
import { FileService } from '@/services/fileService';

interface PersonalInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  postalCode: string;
  country: string;
  dateOfBirth?: string;
  placeOfBirth?: string;
  nationality?: string;
  maritalStatus?: string;
  summary?: string;
  website?: string;
  linkedin?: string;
  photo?: string;
}

interface PersonalInfoSectionProps {
  cvId: string | null;
  onComplete: () => void;
  isCompleted: boolean;
}

export const PersonalInfoSection: React.FC<PersonalInfoSectionProps> = ({
  cvId,
  onComplete,
  isCompleted
}) => {
  const { toast } = useToast();
  const { currentCV } = useCVStore();
  const [formData, setFormData] = useState<PersonalInfo>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    postalCode: '',
    country: '',
    dateOfBirth: '',
    placeOfBirth: '',
    nationality: '',
    maritalStatus: '',
    summary: '',
    website: '',
    linkedin: '',
    photo: ''
  });

  // Populate form data from CV when available
  useEffect(() => {
    if (currentCV?.personal_info) {
      const personalInfo = currentCV.personal_info;
      setFormData({
        firstName: personalInfo.firstName || '',
        lastName: personalInfo.lastName || '',
        email: personalInfo.email || '',
        phone: personalInfo.phone || '',
        address: personalInfo.address || '',
        city: personalInfo.city || '',
        postalCode: personalInfo.postalCode || '',
        country: personalInfo.country || '',
        dateOfBirth: personalInfo.dateOfBirth || '',
        placeOfBirth: personalInfo.placeOfBirth || '',
        nationality: personalInfo.nationality || '',
        maritalStatus: personalInfo.maritalStatus || '',
        summary: personalInfo.summary || '',
        website: personalInfo.website || '',
        linkedin: personalInfo.linkedin || '',
        photo: personalInfo.photo || ''
      });
    }
  }, [currentCV]);

  const [errors, setErrors] = useState<Partial<PersonalInfo>>({});
  const [isUploading, setIsUploading] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: Partial<PersonalInfo> = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    }

    if (!formData.city.trim()) {
      newErrors.city = 'City is required';
    }

    if (!formData.country.trim()) {
      newErrors.country = 'Country is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof PersonalInfo, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handlePhotoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!cvId) {
      toast.error('CV not found', 'Please try refreshing the page');
      return;
    }

    // Validate file using FileService
    const validation = FileService.validateFile(file, 'profile_photo');
    if (!validation.isValid) {
      toast.error('Invalid file', validation.error);
      return;
    }

    // Validate image dimensions
    const dimensionValidation = await FileService.validateImageDimensions(file, 100, 100);
    if (!dimensionValidation.isValid) {
      toast.error('Invalid image dimensions', dimensionValidation.error);
      return;
    }

    setIsUploading(true);
    try {
      // Use FileService for upload
      const uploadedFile = await FileService.uploadFile({
        cvId,
        file,
        category: 'profile_photo'
      });

      // Update form data with the uploaded file URL
      setFormData(prev => ({ ...prev, photo: uploadedFile.url }));
      setIsUploading(false);
      toast.success('Photo uploaded successfully');
    } catch (error: any) {
      setIsUploading(false);
      toast.error('Upload failed', error.message || 'Please try again');
    }
  };

  const handleRemovePhoto = () => {
    setFormData(prev => ({ ...prev, photo: '' }));
  };

  const handleSave = async () => {
    if (!validateForm()) {
      toast.error('Please fix the errors', 'Some required fields are missing or invalid');
      return;
    }

    if (!cvId) {
      toast.error('CV not found', 'Please try refreshing the page');
      return;
    }

    try {
      // Use the CV store to update personal info via API
      // This will call PUT /cvs/{cv_id}/personal-info as per CVManagement.md
      const { updatePersonalInfo } = useCVStore.getState();
      await updatePersonalInfo(cvId, formData);

      onComplete();
      toast.success('Personal information saved!', 'Ready to move to the next section');
    } catch (error: any) {
      toast.error('Failed to save', error.message || 'Please try again');
    }
  };

  // Auto-save on form changes (debounced)
  useEffect(() => {
    const timer = setTimeout(() => {
      if (cvId && Object.values(formData).some(value => value.trim())) {
        // Auto-save logic here
        console.log('Auto-saving personal info...');
      }
    }, 2000);

    return () => clearTimeout(timer);
  }, [formData, cvId]);

  return (
    <div className="space-y-8">
      {/* Photo Upload Section */}
      <FadeInUp>
        <div className="text-center">
          <h3 className="text-lg font-semibold mb-4">Profile Photo (Optional)</h3>
          
          <div className="flex justify-center mb-6">
            <div className="relative">
              {formData.photo ? (
                <div className="relative group">
                  <motion.img
                    src={formData.photo}
                    alt="Profile"
                    className="w-32 h-32 rounded-full object-cover border-4 border-border shadow-lg"
                    whileHover={{ scale: 1.05 }}
                  />
                  <motion.button
                    onClick={handleRemovePhoto}
                    className="absolute -top-2 -right-2 bg-destructive text-white rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <X className="h-4 w-4" />
                  </motion.button>
                </div>
              ) : (
                <motion.label
                  htmlFor="photo-upload"
                  className="w-32 h-32 rounded-full border-2 border-dashed border-border hover:border-primary flex flex-col items-center justify-center cursor-pointer bg-secondary/30 hover:bg-secondary/50 transition-colors group"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {isUploading ? (
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    >
                      <Upload className="h-6 w-6 text-primary" />
                    </motion.div>
                  ) : (
                    <>
                      <Camera className="h-6 w-6 text-foreground-muted group-hover:text-primary transition-colors" />
                      <span className="text-xs text-foreground-muted group-hover:text-primary transition-colors mt-1">
                        Add Photo
                      </span>
                    </>
                  )}
                </motion.label>
              )}
              
              <input
                id="photo-upload"
                type="file"
                accept="image/*"
                onChange={handlePhotoUpload}
                className="hidden"
                disabled={isUploading}
              />
            </div>
          </div>
          
          <p className="text-sm text-foreground-muted">
            Upload a professional headshot (optional). Max size: 5MB
          </p>
        </div>
      </FadeInUp>

      {/* Personal Information Form */}
      <FadeInUp delay={0.1}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Name Fields */}
          <Input
            label="First Name"
            value={formData.firstName}
            onChange={(e) => handleInputChange('firstName', e.target.value)}
            error={errors.firstName}
            placeholder="Enter your first name"
            required
            icon={<User className="h-4 w-4" />}
          />

          <Input
            label="Last Name"
            value={formData.lastName}
            onChange={(e) => handleInputChange('lastName', e.target.value)}
            error={errors.lastName}
            placeholder="Enter your last name"
            required
            icon={<User className="h-4 w-4" />}
          />

          {/* Contact Fields */}
          <Input
            label="Email Address"
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            error={errors.email}
            placeholder="<EMAIL>"
            required
            icon={<Mail className="h-4 w-4" />}
          />

          <Input
            label="Phone Number"
            type="tel"
            value={formData.phone}
            onChange={(e) => handleInputChange('phone', e.target.value)}
            error={errors.phone}
            placeholder="+****************"
            required
            icon={<Phone className="h-4 w-4" />}
          />

          {/* Address Fields */}
          <Input
            label="Address"
            value={formData.address}
            onChange={(e) => handleInputChange('address', e.target.value)}
            error={errors.address}
            placeholder="Street address"
            icon={<MapPin className="h-4 w-4" />}
          />

          <Input
            label="City"
            value={formData.city}
            onChange={(e) => handleInputChange('city', e.target.value)}
            error={errors.city}
            placeholder="City"
            required
            icon={<MapPin className="h-4 w-4" />}
          />

          <Input
            label="Postal Code"
            value={formData.postalCode}
            onChange={(e) => handleInputChange('postalCode', e.target.value)}
            placeholder="12345"
            icon={<MapPin className="h-4 w-4" />}
          />

          <Input
            label="Country"
            value={formData.country}
            onChange={(e) => handleInputChange('country', e.target.value)}
            error={errors.country}
            placeholder="Country"
            required
            icon={<Globe className="h-4 w-4" />}
          />

          {/* Additional Personal Details */}
          <Input
            label="Date of Birth (Optional)"
            type="date"
            value={formData.dateOfBirth}
            onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
            placeholder="YYYY-MM-DD"
          />

          <Input
            label="Place of Birth (Optional)"
            value={formData.placeOfBirth}
            onChange={(e) => handleInputChange('placeOfBirth', e.target.value)}
            placeholder="City, Country"
            icon={<MapPin className="h-4 w-4" />}
          />

          <Input
            label="Nationality (Optional)"
            value={formData.nationality}
            onChange={(e) => handleInputChange('nationality', e.target.value)}
            placeholder="Your nationality"
            icon={<Globe className="h-4 w-4" />}
          />

          <div>
            <label className="block text-sm font-medium mb-2">
              Marital Status (Optional)
            </label>
            <select
              value={formData.maritalStatus}
              onChange={(e) => handleInputChange('maritalStatus', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-lg bg-background focus:border-primary focus:ring-2 focus:ring-primary/20 transition-colors"
            >
              <option value="">Select status</option>
              <option value="single">Single</option>
              <option value="married">Married</option>
              <option value="divorced">Divorced</option>
              <option value="widowed">Widowed</option>
            </select>
          </div>

          {/* Optional Fields */}
          <Input
            label="Website (Optional)"
            type="url"
            value={formData.website}
            onChange={(e) => handleInputChange('website', e.target.value)}
            placeholder="https://yourwebsite.com"
            icon={<Globe className="h-4 w-4" />}
          />

          <Input
            label="LinkedIn (Optional)"
            type="url"
            value={formData.linkedin}
            onChange={(e) => handleInputChange('linkedin', e.target.value)}
            placeholder="https://linkedin.com/in/yourprofile"
            icon={<Globe className="h-4 w-4" />}
          />
        </div>
      </FadeInUp>

      {/* Professional Summary */}
      <FadeInUp delay={0.2}>
        <div className="bg-background-elevated rounded-2xl p-6 border border-border">
          <h4 className="text-lg font-medium mb-4">Professional Summary (Optional)</h4>
          <textarea
            value={formData.summary}
            onChange={(e) => handleInputChange('summary', e.target.value)}
            placeholder="Write a brief professional summary that highlights your key skills, experience, and career objectives..."
            className="w-full p-4 border border-border rounded-lg bg-background focus:border-primary focus:ring-2 focus:ring-primary/20 transition-colors resize-none"
            rows={4}
          />
          <p className="text-xs text-foreground-muted mt-2">
            A compelling summary can help recruiters quickly understand your value proposition
          </p>
        </div>
      </FadeInUp>

      {/* Save Button */}
      <FadeInUp delay={0.2} className="flex justify-end pt-6 border-t border-border">
        <MagneticElement>
          <Button
            onClick={handleSave}
            size="lg"
            gradient={!isCompleted}
            variant={isCompleted ? "success" : "primary"}
            className="group"
          >
            {isCompleted ? (
              <>
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ type: "spring", stiffness: 500, damping: 30 }}
                >
                  <User className="h-5 w-5 mr-2" />
                </motion.div>
                Information Saved
              </>
            ) : (
              <>
                Save & Continue
                <motion.div
                  className="ml-2"
                  animate={{ x: [0, 4, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                >
                  <User className="h-5 w-5" />
                </motion.div>
              </>
            )}
          </Button>
        </MagneticElement>
      </FadeInUp>
    </div>
  );
};
