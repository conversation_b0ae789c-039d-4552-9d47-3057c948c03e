'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Users, Plus, Mail, Phone, Building } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { FadeInUp, MagneticElement } from '@/components/motion/MotionComponents';
import { useToast } from '@/components/ui/ToastNotification';

interface Reference {
  id: string;
  name: string;
  position: string;
  company: string;
  email: string;
  phone: string;
  relationship: string;
}

interface ReferencesSectionProps {
  cvId: string | null;
  onComplete: () => void;
  isCompleted: boolean;
}

export const ReferencesSection: React.FC<ReferencesSectionProps> = ({
  cvId,
  onComplete,
  isCompleted
}) => {
  const { toast } = useToast();
  const [references, setReferences] = useState<Reference[]>([]);
  const [availableOnRequest, setAvailableOnRequest] = useState(true);

  const addReference = () => {
    const newReference: Reference = {
      id: Date.now().toString(),
      name: '',
      position: '',
      company: '',
      email: '',
      phone: '',
      relationship: ''
    };
    setReferences([...references, newReference]);
    setAvailableOnRequest(false);
  };

  const updateReference = (id: string, field: keyof Reference, value: string) => {
    setReferences(prev =>
      prev.map(ref => (ref.id === id ? { ...ref, [field]: value } : ref))
    );
  };

  const removeReference = (id: string) => {
    setReferences(prev => prev.filter(ref => ref.id !== id));
    if (references.length === 1) {
      setAvailableOnRequest(true);
    }
  };

  const handleSave = () => {
    if (!availableOnRequest && references.length === 0) {
      toast.error('Please add at least one reference or select "Available on request"');
      return;
    }

    onComplete();
    toast.success('References saved!');
  };

  return (
    <div className="space-y-8">
      <FadeInUp>
        <div className="text-center mb-8">
          <h3 className="text-lg font-semibold mb-2">Professional References</h3>
          <p className="text-foreground-muted">
            Add references who can vouch for your professional abilities
          </p>
        </div>
      </FadeInUp>

      {/* Available on Request Option */}
      <FadeInUp>
        <div className="bg-background-elevated rounded-2xl p-6 border border-border">
          <div className="flex items-center gap-3">
            <input
              type="checkbox"
              id="available-on-request"
              checked={availableOnRequest}
              onChange={(e) => {
                setAvailableOnRequest(e.target.checked);
                if (e.target.checked) {
                  setReferences([]);
                }
              }}
              className="h-4 w-4 text-primary focus:ring-primary border-border rounded"
            />
            <label htmlFor="available-on-request" className="text-sm font-medium">
              References available upon request
            </label>
          </div>
          <p className="text-xs text-foreground-muted mt-2 ml-7">
            This is a common practice and saves space on your CV
          </p>
        </div>
      </FadeInUp>

      {!availableOnRequest && (
        <>
          {/* References List */}
          <div className="space-y-6">
            {references.map((reference, index) => (
              <FadeInUp key={reference.id} delay={index * 0.1}>
                <motion.div
                  className="bg-background-elevated rounded-2xl p-6 border border-border"
                  whileHover={{ y: -2 }}
                  transition={{ type: "spring", stiffness: 300, damping: 30 }}
                >
                  <div className="flex justify-between items-center mb-6">
                    <h4 className="text-lg font-medium flex items-center gap-2">
                      <Users className="h-5 w-5 text-primary" />
                      Reference {index + 1}
                    </h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeReference(reference.id)}
                      className="text-destructive hover:text-destructive"
                    >
                      Remove
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="Full Name"
                      value={reference.name}
                      onChange={(e) => updateReference(reference.id, 'name', e.target.value)}
                      placeholder="Reference's full name"
                      required
                    />

                    <Input
                      label="Position"
                      value={reference.position}
                      onChange={(e) => updateReference(reference.id, 'position', e.target.value)}
                      placeholder="Job title"
                      required
                    />

                    <Input
                      label="Company"
                      value={reference.company}
                      onChange={(e) => updateReference(reference.id, 'company', e.target.value)}
                      placeholder="Company name"
                      icon={<Building className="h-4 w-4" />}
                    />

                    <Input
                      label="Relationship"
                      value={reference.relationship}
                      onChange={(e) => updateReference(reference.id, 'relationship', e.target.value)}
                      placeholder="Former manager, colleague, etc."
                    />

                    <Input
                      label="Email"
                      type="email"
                      value={reference.email}
                      onChange={(e) => updateReference(reference.id, 'email', e.target.value)}
                      placeholder="<EMAIL>"
                      icon={<Mail className="h-4 w-4" />}
                    />

                    <Input
                      label="Phone"
                      type="tel"
                      value={reference.phone}
                      onChange={(e) => updateReference(reference.id, 'phone', e.target.value)}
                      placeholder="+****************"
                      icon={<Phone className="h-4 w-4" />}
                    />
                  </div>
                </motion.div>
              </FadeInUp>
            ))}
          </div>

          {/* Add Reference Button */}
          <FadeInUp>
            <div className="flex justify-center">
              <MagneticElement>
                <Button
                  onClick={addReference}
                  variant="outline"
                  className="group"
                >
                  <Plus className="h-4 w-4 mr-2 group-hover:rotate-90 transition-transform" />
                  Add Reference
                </Button>
              </MagneticElement>
            </div>
          </FadeInUp>
        </>
      )}

      <FadeInUp className="flex justify-end pt-6 border-t border-border">
        <MagneticElement>
          <Button
            onClick={handleSave}
            size="lg"
            gradient={!isCompleted}
            variant={isCompleted ? "success" : "primary"}
            className="group"
          >
            {isCompleted ? (
              <>
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ type: "spring", stiffness: 500, damping: 30 }}
                >
                  <Users className="h-5 w-5 mr-2" />
                </motion.div>
                References Saved
              </>
            ) : (
              <>
                Save & Continue
                <motion.div
                  className="ml-2"
                  animate={{ x: [0, 4, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                >
                  <Users className="h-5 w-5" />
                </motion.div>
              </>
            )}
          </Button>
        </MagneticElement>
      </FadeInUp>
    </div>
  );
};
