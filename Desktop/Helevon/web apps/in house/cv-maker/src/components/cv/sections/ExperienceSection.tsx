'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Briefcase, Plus, Calendar, MapPin, Building } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { FadeInUp, MagneticElement } from '@/components/motion/MotionComponents';
import { useToast } from '@/components/ui/ToastNotification';

interface Experience {
  id: string;
  company: string;
  position: string;
  location: string;
  startDate: string;
  endDate: string;
  current: boolean;
  description: string;
}

interface ExperienceSectionProps {
  cvId: string | null;
  onComplete: () => void;
  isCompleted: boolean;
}

export const ExperienceSection: React.FC<ExperienceSectionProps> = ({
  cvId,
  onComplete,
  isCompleted
}) => {
  const { toast } = useToast();
  const [experienceList, setExperienceList] = useState<Experience[]>([
    {
      id: '1',
      company: '',
      position: '',
      location: '',
      startDate: '',
      endDate: '',
      current: false,
      description: ''
    }
  ]);

  const addExperience = () => {
    const newExperience: Experience = {
      id: Date.now().toString(),
      company: '',
      position: '',
      location: '',
      startDate: '',
      endDate: '',
      current: false,
      description: ''
    };
    setExperienceList([...experienceList, newExperience]);
  };

  const updateExperience = (id: string, field: keyof Experience, value: string | boolean) => {
    setExperienceList(prev =>
      prev.map(exp => (exp.id === id ? { ...exp, [field]: value } : exp))
    );
  };

  const removeExperience = (id: string) => {
    if (experienceList.length > 1) {
      setExperienceList(prev => prev.filter(exp => exp.id !== id));
    }
  };

  const handleSave = () => {
    const hasValidEntry = experienceList.some(exp => 
      exp.company.trim() && exp.position.trim()
    );

    if (!hasValidEntry) {
      toast.error('Please fill in at least one work experience', 'Company and position are required');
      return;
    }

    onComplete();
    toast.success('Work experience saved!');
  };

  return (
    <div className="space-y-8">
      <FadeInUp>
        <div className="text-center mb-8">
          <h3 className="text-lg font-semibold mb-2">Work Experience</h3>
          <p className="text-foreground-muted">
            Showcase your professional journey and achievements
          </p>
        </div>
      </FadeInUp>

      <div className="space-y-6">
        {experienceList.map((experience, index) => (
          <FadeInUp key={experience.id} delay={index * 0.1}>
            <motion.div
              className="bg-background-elevated rounded-2xl p-6 border border-border"
              whileHover={{ y: -2 }}
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
            >
              <div className="flex justify-between items-center mb-6">
                <h4 className="text-lg font-medium flex items-center gap-2">
                  <Briefcase className="h-5 w-5 text-primary" />
                  Experience {index + 1}
                </h4>
                {experienceList.length > 1 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeExperience(experience.id)}
                    className="text-destructive hover:text-destructive"
                  >
                    Remove
                  </Button>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Company"
                  value={experience.company}
                  onChange={(e) => updateExperience(experience.id, 'company', e.target.value)}
                  placeholder="Company name"
                  required
                  icon={<Building className="h-4 w-4" />}
                />

                <Input
                  label="Position"
                  value={experience.position}
                  onChange={(e) => updateExperience(experience.id, 'position', e.target.value)}
                  placeholder="Job title"
                  required
                />

                <Input
                  label="Location"
                  value={experience.location}
                  onChange={(e) => updateExperience(experience.id, 'location', e.target.value)}
                  placeholder="City, Country"
                  icon={<MapPin className="h-4 w-4" />}
                />

                <div className="flex items-center gap-3">
                  <input
                    type="checkbox"
                    id={`current-${experience.id}`}
                    checked={experience.current}
                    onChange={(e) => updateExperience(experience.id, 'current', e.target.checked)}
                    className="h-4 w-4 text-primary focus:ring-primary border-border rounded"
                  />
                  <label htmlFor={`current-${experience.id}`} className="text-sm font-medium">
                    I currently work here
                  </label>
                </div>

                <Input
                  label="Start Date"
                  type="month"
                  value={experience.startDate}
                  onChange={(e) => updateExperience(experience.id, 'startDate', e.target.value)}
                  icon={<Calendar className="h-4 w-4" />}
                />

                {!experience.current && (
                  <Input
                    label="End Date"
                    type="month"
                    value={experience.endDate}
                    onChange={(e) => updateExperience(experience.id, 'endDate', e.target.value)}
                    icon={<Calendar className="h-4 w-4" />}
                  />
                )}
              </div>

              <div className="mt-4">
                <label className="block text-sm font-medium mb-2">
                  Job Description & Achievements
                </label>
                <textarea
                  value={experience.description}
                  onChange={(e) => updateExperience(experience.id, 'description', e.target.value)}
                  placeholder="• Describe your key responsibilities and achievements&#10;• Use bullet points for better readability&#10;• Include quantifiable results when possible"
                  className="w-full p-3 border border-border rounded-lg bg-background focus:border-primary focus:ring-2 focus:ring-primary/20 transition-colors resize-none"
                  rows={5}
                />
              </div>
            </motion.div>
          </FadeInUp>
        ))}
      </div>

      <FadeInUp>
        <div className="flex justify-center">
          <MagneticElement>
            <Button
              onClick={addExperience}
              variant="outline"
              className="group"
            >
              <Plus className="h-4 w-4 mr-2 group-hover:rotate-90 transition-transform" />
              Add Another Experience
            </Button>
          </MagneticElement>
        </div>
      </FadeInUp>

      <FadeInUp className="flex justify-end pt-6 border-t border-border">
        <MagneticElement>
          <Button
            onClick={handleSave}
            size="lg"
            gradient={!isCompleted}
            variant={isCompleted ? "success" : "primary"}
            className="group"
          >
            {isCompleted ? (
              <>
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ type: "spring", stiffness: 500, damping: 30 }}
                >
                  <Briefcase className="h-5 w-5 mr-2" />
                </motion.div>
                Experience Saved
              </>
            ) : (
              <>
                Save & Continue
                <motion.div
                  className="ml-2"
                  animate={{ x: [0, 4, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                >
                  <Briefcase className="h-5 w-5" />
                </motion.div>
              </>
            )}
          </Button>
        </MagneticElement>
      </FadeInUp>
    </div>
  );
};
