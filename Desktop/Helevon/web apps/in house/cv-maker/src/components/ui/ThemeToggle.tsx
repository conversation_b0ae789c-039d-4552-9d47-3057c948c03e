'use client';

import React from 'react';
import { <PERSON>, <PERSON>, Monitor } from 'lucide-react';
import { Button } from './Button';
import { Dropdown } from './Dropdown';
import { useTheme } from '@/components/providers/ThemeProvider';

export const ThemeToggle: React.FC = () => {
  const { theme, setTheme } = useTheme();

  const themeOptions = [
    {
      value: 'light',
      label: 'Light',
      icon: <Sun className="h-4 w-4" />,
    },
    {
      value: 'dark',
      label: 'Dark',
      icon: <Moon className="h-4 w-4" />,
    },
    {
      value: 'system',
      label: 'System',
      icon: <Monitor className="h-4 w-4" />,
    },
  ];

  return (
    <Dropdown
      options={themeOptions}
      value={theme}
      onChange={(value) => setTheme(value as 'light' | 'dark' | 'system')}
      className="w-32"
    />
  );
};

// Simple toggle button version
export const SimpleThemeToggle: React.FC = () => {
  const { actualTheme, setTheme } = useTheme();

  const toggleTheme = () => {
    setTheme(actualTheme === 'light' ? 'dark' : 'light');
  };

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={toggleTheme}
      className="h-9 w-9 p-0"
      aria-label="Toggle theme"
    >
      {actualTheme === 'light' ? (
        <Moon className="h-4 w-4" />
      ) : (
        <Sun className="h-4 w-4" />
      )}
    </Button>
  );
};
