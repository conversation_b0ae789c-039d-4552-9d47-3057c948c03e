'use client';

import React from 'react';
import Link from 'next/link';
import { motion, HTMLMotionProps } from 'framer-motion';
import { cn } from '@/lib/utils';
import { buttonHover, buttonTap } from '@/lib/animations';

export interface ButtonProps extends Omit<HTMLMotionProps<'button'>, 'children'> {
  variant?: 'primary' | 'secondary' | 'accent' | 'success' | 'warning' | 'danger' | 'outline' | 'ghost' | 'glass';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  isLoading?: boolean;
  children: React.ReactNode;
  asChild?: boolean;
  href?: string;
  glow?: boolean;
  gradient?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({
    className,
    variant = 'primary',
    size = 'md',
    isLoading = false,
    disabled,
    children,
    asChild = false,
    href,
    glow = false,
    gradient = false,
    ...props
  }, ref) => {
    const baseStyles = 'relative inline-flex items-center justify-center font-medium transition-all duration-200 focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50 overflow-hidden group';

    const variants = {
      primary: gradient
        ? 'bg-gradient-to-r from-primary to-primary-dark text-primary-foreground shadow-lg hover:shadow-xl animate-gradient bg-[length:200%_200%]'
        : 'bg-primary text-primary-foreground hover:bg-primary-dark shadow-md hover:shadow-lg',
      secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary-dark border border-border hover:border-border-light',
      accent: gradient
        ? 'bg-gradient-to-r from-accent to-accent-dark text-accent-foreground shadow-lg hover:shadow-xl animate-gradient bg-[length:200%_200%]'
        : 'bg-accent text-accent-foreground hover:bg-accent-dark shadow-md hover:shadow-lg',
      success: 'bg-success text-success-foreground hover:bg-success-light shadow-md hover:shadow-lg',
      warning: 'bg-warning text-warning-foreground hover:bg-warning-light shadow-md hover:shadow-lg',
      danger: 'bg-destructive text-destructive-foreground hover:bg-destructive-light shadow-md hover:shadow-lg',
      outline: 'border-2 border-primary text-primary hover:bg-primary hover:text-primary-foreground hover:shadow-md',
      ghost: 'text-foreground hover:bg-secondary hover:text-secondary-foreground',
      glass: 'glass text-foreground hover:bg-white/20 backdrop-blur-xl border border-white/20',
    };

    const sizes = {
      sm: 'h-9 px-4 text-sm rounded-lg',
      md: 'h-11 px-6 text-base rounded-xl',
      lg: 'h-13 px-8 text-lg rounded-xl',
      xl: 'h-16 px-10 text-xl rounded-2xl',
    };

    const glowStyles = glow ? {
      primary: 'glow-primary',
      accent: 'glow-accent',
      success: 'shadow-[0_0_20px_rgba(16,185,129,0.4)]',
      warning: 'shadow-[0_0_20px_rgba(245,158,11,0.4)]',
      danger: 'shadow-[0_0_20px_rgba(239,68,68,0.4)]',
    } : {};

    const classNames = cn(
      baseStyles,
      variants[variant],
      sizes[size],
      glow && glowStyles[variant as keyof typeof glowStyles],
      isLoading && 'cursor-not-allowed',
      className
    );

    const content = (
      <>
        {/* Shimmer effect */}
        <div className="absolute inset-0 -top-px overflow-hidden rounded-xl">
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out" />
        </div>

        {/* Loading spinner */}
        {isLoading && (
          <motion.div
            className="mr-2"
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
          >
            <svg
              className="h-4 w-4"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              />
              <path
                className="opacity-75"
                fill="currentColor"
                d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              />
            </svg>
          </motion.div>
        )}

        {/* Content */}
        <span className="relative z-10">{children}</span>
      </>
    );

    if (asChild && href) {
      return (
        <Link href={href}>
          <motion.div
            className={classNames}
            whileHover={!disabled && !isLoading ? buttonHover : undefined}
            whileTap={!disabled && !isLoading ? buttonTap : undefined}
          >
            {content}
          </motion.div>
        </Link>
      );
    }

    return (
      <motion.button
        className={classNames}
        ref={ref}
        disabled={disabled || isLoading}
        whileHover={!disabled && !isLoading ? buttonHover : undefined}
        whileTap={!disabled && !isLoading ? buttonTap : undefined}
        {...props}
      >
        {content}
      </motion.button>
    );
  }
);

Button.displayName = 'Button';

export { Button };
